import React, { useState, useEffect } from 'react';
import { MessageSquare, Play, Square, Settings, User, Key, <PERSON>ie, AlertCircle, CheckCircle, Loader, LogOut, RefreshCw, LogIn } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import toast, { Toaster } from 'react-hot-toast';
import api from '../config/api';
import LoginPopup from './LoginPopup';

const MessengerControl = () => {
  const { socket, isConnected } = useSocket();
  const [messengerStatus, setMessengerStatus] = useState({
    isRunning: false,
    isLoggedIn: false,
    queueLength: 0,
    isProcessingQueue: false,
    browserConnected: false,
    pageConnected: false,
    currentUsername: null
  });

  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });

  const [savedCookies, setSavedCookies] = useState(null);
  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  useEffect(() => {
    loadMessengerStatus();
    loadSavedCookies();
  }, []);

  useEffect(() => {
    if (!socket) return;

    const handleMessengerConnected = () => {
      setMessengerStatus(prev => ({ ...prev, isRunning: true, isLoggedIn: true }));
      setIsStarting(false);
      toast.success('Instagram Messenger đã kết nối thành công!');
    };

    const handleMessengerDisconnected = () => {
      setMessengerStatus(prev => ({
        ...prev,
        isRunning: false,
        isLoggedIn: false,
        queueLength: 0,
        isProcessingQueue: false
      }));
      setIsStopping(false);
      toast.info('Instagram Messenger đã ngắt kết nối');
    };

    const handleMessageQueued = (data) => {
      setMessengerStatus(prev => ({ ...prev, queueLength: prev.queueLength + 1 }));
      toast.success(`Tin nhắn đã được thêm vào hàng đợi: @${data.username}`);
    };

    const handleMessageStatusUpdate = (data) => {
      if (data.status === 'sent') {
        setMessengerStatus(prev => ({ ...prev, queueLength: Math.max(0, prev.queueLength - 1) }));
        toast.success(`✅ Tin nhắn đã gửi thành công đến @${data.username}`);
      } else if (data.status === 'skipped') {
        setMessengerStatus(prev => ({ ...prev, queueLength: Math.max(0, prev.queueLength - 1) }));
        toast.warning(`⏭️ Bỏ qua tin nhắn đến @${data.username}: ${data.error}`, {
          duration: 4000,
          icon: '⚠️'
        });
      } else if (data.status === 'failed') {
        if (data.retries < data.maxRetries) {
          toast.warning(`⚠️ Gửi tin nhắn thất bại đến @${data.username}, đang thử lại... (${data.retries}/${data.maxRetries})`);
        } else {
          setMessengerStatus(prev => ({ ...prev, queueLength: Math.max(0, prev.queueLength - 1) }));
          toast.error(`❌ Gửi tin nhắn thất bại đến @${data.username}: ${data.error}`);
        }
      } else if (data.status === 'sending') {
        toast.info(`📤 Đang gửi tin nhắn đến @${data.username}...`);
      } else if (data.status === 'pending') {
        toast.info(`⏳ Tin nhắn đến @${data.username} đang chờ xử lý...`);
      }
    };

    const handleCookiesSaved = (data) => {
      // Reload saved cookies when they're saved
      loadSavedCookies();
      toast.success(`Cookies đã được lưu cho tài khoản @${data.username}`);
    };

    // Thêm xử lý các sự kiện liên quan đến tự động khởi động lại
    const handleMessengerRestarting = (data) => {
      if (data.reason === 'max_messages_reached') {
        toast.info(`Đang dừng messenger sau khi gửi ${data.messageCount} tin nhắn...`, {
          duration: 5000
        });
        // Không thay đổi trạng thái isRunning ở đây vì messenger vẫn đang chạy
      }
    };

    const handleMessengerStopped = (data) => {
      if (data.reason === 'max_messages_reached') {
        // Cập nhật trạng thái isRunning thành false khi messenger dừng
        setMessengerStatus(prev => ({ ...prev, isRunning: false }));
        toast.success(`Đã dừng messenger sau khi gửi đủ ${data.maxMessages} tin nhắn. Đang khởi động lại...`, {
          duration: 3000
        });
      }
    };

    const handleMessengerStopCompleted = (data) => {
      console.log('Messenger stop completed:', data);
      // Không cần thay đổi trạng thái ở đây vì đã cập nhật trong handleMessengerStopped
    };

    const handleMessengerRestarted = (data) => {
      if (data.reason === 'max_messages_reached') {
        // Cập nhật trạng thái isRunning thành true khi messenger khởi động lại
        setMessengerStatus(prev => ({ ...prev, isRunning: true, isLoggedIn: true }));
        toast.success(`Đã khởi động lại messenger thành công sau khi gửi đủ ${data.maxMessages} tin nhắn`, {
          duration: 3000
        });
        // Cập nhật lại toàn bộ trạng thái messenger từ server
        loadMessengerStatus();
      }
    };

    const handleMessengerRestartFailed = (data) => {
      if (data.reason === 'max_messages_reached') {
        // Giữ trạng thái isRunning là false khi khởi động lại thất bại
        setMessengerStatus(prev => ({ ...prev, isRunning: false, isLoggedIn: false }));
        toast.error(`Không thể khởi động lại messenger: ${data.message || data.error}`, {
          duration: 8000
        });
      }
    };

    socket.on('messenger-connected', handleMessengerConnected);
    socket.on('messenger-disconnected', handleMessengerDisconnected);
    socket.on('message-queued', handleMessageQueued);
    socket.on('message-status-update', handleMessageStatusUpdate);
    socket.on('messageStatusUpdate', handleMessageStatusUpdate); // Alternative event name
    socket.on('messenger-cookies-saved', handleCookiesSaved);
    socket.on('messenger-restarting', handleMessengerRestarting);
    socket.on('messenger-stopped', handleMessengerStopped);
    socket.on('messenger-stop-completed', handleMessengerStopCompleted);
    socket.on('messenger-restarted', handleMessengerRestarted);
    socket.on('messenger-restart-failed', handleMessengerRestartFailed);

    return () => {
      socket.off('messenger-connected', handleMessengerConnected);
      socket.off('messenger-disconnected', handleMessengerDisconnected);
      socket.off('message-queued', handleMessageQueued);
      socket.off('message-status-update', handleMessageStatusUpdate);
      socket.off('messageStatusUpdate', handleMessageStatusUpdate);
      socket.off('messenger-cookies-saved', handleCookiesSaved);
      socket.off('messenger-restarting', handleMessengerRestarting);
      socket.off('messenger-stopped', handleMessengerStopped);
      socket.off('messenger-stop-completed', handleMessengerStopCompleted);
      socket.off('messenger-restarted', handleMessengerRestarted);
      socket.off('messenger-restart-failed', handleMessengerRestartFailed);
    };
  }, [socket]);

  const loadMessengerStatus = async () => {
    try {
      const response = await fetch('/api/messenger-status');
      const data = await response.json();
      if (data.success) {
        setMessengerStatus(data.status);
      }
    } catch (error) {
      console.error('Failed to load messenger status:', error);
    }
  };

  const loadSavedCookies = async () => {
    try {
      const response = await api.get('/api/messenger-saved-cookies');
      if (response.data.success && response.data.hasCookies) {
        setSavedCookies({
          username: response.data.username
        });
        console.log('Loaded saved cookies for:', response.data.username);
      } else {
        // No cookies found, clear state
        setSavedCookies(null);
        console.log('No saved cookies found, cleared state');
      }
    } catch (error) {
      console.error('Failed to load saved cookies:', error);
      setSavedCookies(null); // Clear state on error
    }
  };

  const handleStartMessenger = async () => {
    if (!isConnected) {
      toast.error('Không có kết nối với server');
      return;
    }

    // Use saved cookies if available, otherwise use form input
    let credentialsToUse = null;
    let useSavedCookies = false;

    if (savedCookies) {
      useSavedCookies = true;
      toast('Đang sử dụng phiên đăng nhập đã lưu...', {
        icon: 'ℹ️',
        duration: 5000
      });
    } else {
      if (!credentials.username.trim() || !credentials.password.trim()) {
        toast.error('Vui lòng nhập thông tin đăng nhập Instagram');
        return;
      }
      credentialsToUse = {
        username: credentials.username.trim(),
        password: credentials.password.trim()
      };
    }

    setIsStarting(true);

    try {
      const response = await api.post('/api/start-messenger', {
        credentials: credentialsToUse,
        useSavedCookies
      });

      if (response.data.pending2FA) {
        toast.error('Cần xác thực 2FA! Vui lòng nhập mã xác thực trong cửa sổ trình duyệt.', {
          duration: 10000,
          icon: '🔐'
        });
        setIsStarting(false);
        return;
      }

      if (response.data.success) {
        if (useSavedCookies) {
          toast.success('Đã bắt đầu Instagram Messenger bằng phiên đăng nhập đã lưu', {
            duration: 6000
          });
          toast('Đang chờ xác thực đăng nhập... Có thể mất 1-2 phút để kết nối hoàn toàn.', {
            icon: '⏳',
            duration: 8000
          });
        } else {
          toast.success('Đã bắt đầu Instagram Messenger - Phiên đăng nhập đã được lưu');
          // Reload saved cookies in case they were just saved
          await loadSavedCookies();
        }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi khởi động messenger';
      toast.error(errorMessage);
      console.error('Start messenger error:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const handleStopMessenger = async () => {
    if (!isConnected) {
      toast.error('Không có kết nối với server');
      return;
    }

    setIsStopping(true);

    try {
      const response = await api.post('/api/stop-messenger');

      if (response.data.success) {
        toast.success('Instagram Messenger đã dừng');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi dừng messenger';
      toast.error(errorMessage);
      console.error('Stop messenger error:', error);
    } finally {
      setIsStopping(false);
    }
  };

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      const response = await api.post('/api/logout-messenger');
      if (response.data.success) {
        // Clear local state first
        setSavedCookies(null);
        setCredentials({ username: '', password: '' });
        setMessengerStatus(prev => ({ ...prev, currentUsername: null }));

        // Wait a bit for database to complete, then verify cookies are actually cleared
        setTimeout(async () => {
          try {
            const verifyResponse = await api.get('/api/messenger-saved-cookies');
            if (verifyResponse.data.success && verifyResponse.data.hasCookies) {
              // Cookies still exist! Try again
              console.warn('Cookies still exist after logout, retrying...');
              await api.post('/api/logout-messenger');

              // Check again
              const secondVerify = await api.get('/api/messenger-saved-cookies');
              if (secondVerify.data.success && secondVerify.data.hasCookies) {
                toast.error('Lỗi: Không thể xóa cookies hoàn toàn. Vui lòng thử lại.');
                // Restore UI state if cookies still exist
                setSavedCookies({ username: secondVerify.data.username });
              } else {
                console.log('Cookies successfully cleared on retry');
              }
            } else {
              console.log('Cookies successfully cleared');
            }
          } catch (verifyError) {
            console.error('Failed to verify cookie deletion:', verifyError);
          }
        }, 500); // Wait 500ms for database operation to complete

        toast.success('Đã đăng xuất thành công - Cookies đã được xóa');
      } else {
        throw new Error(response.data.error || 'Logout failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi đăng xuất';
      toast.error(errorMessage);
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handlePopupLogin = async (loginCredentials) => {
    setIsStarting(true);
    try {
      const response = await api.post('/api/start-messenger', {
        credentials: {
          username: loginCredentials.username.trim(),
          password: loginCredentials.password.trim()
        },
        useSavedCookies: false
      });

      if (response.data.pending2FA) {
        toast.error('Cần xác thực 2FA! Vui lòng nhập mã xác thực trong cửa sổ trình duyệt.', {
          duration: 10000,
          icon: '🔐'
        });
        setIsStarting(false);
        return;
      }

      if (response.data.success) {
        toast.success('Đã bắt đầu Instagram Messenger - Phiên đăng nhập đã được lưu');
        setShowLoginPopup(false);
        // Reload saved cookies
        await loadSavedCookies();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Lỗi khi bắt đầu messenger';
      toast.error(errorMessage);
      console.error('Start messenger error:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const getStatusColor = () => {
    if (!messengerStatus.isRunning) return 'text-gray-500';
    if (messengerStatus.isLoggedIn) return 'text-green-600';
    return 'text-yellow-600';
  };

  const getStatusText = () => {
    if (!messengerStatus.isRunning) return 'Đã dừng';
    if (messengerStatus.isLoggedIn) return 'Đang hoạt động';
    return 'Đang kết nối...';
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <MessageSquare className="h-6 w-6 text-purple-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Tin nhắn tự động</h3>
            <p className="text-sm text-gray-500">Gửi tin nhắn tự động khi có bình luận mới</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
            {messengerStatus.isRunning && messengerStatus.isLoggedIn ? (
              <CheckCircle className="h-4 w-4" />
            ) : messengerStatus.isRunning ? (
              <Loader className="h-4 w-4 animate-spin" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <span className="text-sm font-medium">{getStatusText()}</span>
          </div>
          {messengerStatus.currentUsername && (
            <div className="flex items-center space-x-2">
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                @{messengerStatus.currentUsername}
              </div>
              <button
                onClick={handleLogout}
                className="text-xs text-red-600 hover:text-red-800 p-1 hover:bg-red-50 rounded"
                title="Đăng xuất"
              >
                <LogOut className="h-3 w-3" />
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* Credentials */}
        {savedCookies ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-green-800">Phiên đăng nhập đã lưu</h4>
                <p className="text-sm text-green-700">
                  Tài khoản: <span className="font-medium">@{savedCookies.username}</span>
                </p>
                <p className="text-xs text-green-600 mt-1 hidden sm:block">
                  Bạn có thể bắt đầu messenger mà không cần đăng nhập lại. Cookies sẽ tự động được sử dụng.
                </p>
                <p className="text-xs text-green-600 mt-1 sm:hidden">
                  Sẵn sàng gửi tin nhắn với tài khoản đã lưu
                </p>
              </div>
              <button
                onClick={handleLogout}
                disabled={isLoggingOut || messengerStatus.isRunning}
                className="btn-secondary flex items-center justify-center text-sm w-full sm:w-auto"
              >
                {isLoggingOut ? (
                  <div className="spinner mr-2" />
                ) : (
                  <LogOut className="h-4 w-4 mr-2" />
                )}
                {isLoggingOut ? 'Đang xóa cookies...' : 'Xóa cookies'}
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">Cần đăng nhập</h4>
                <p className="text-sm text-red-700">
                  Vui lòng đăng nhập Instagram để bắt đầu gửi tin nhắn tự động
                </p>
              </div>
              <button
                onClick={() => setShowLoginPopup(true)}
                disabled={messengerStatus.isRunning}
                className="btn-primary flex items-center justify-center text-sm w-full sm:w-auto"
              >
                <LogIn className="h-4 w-4 mr-2" />
                Đăng nhập
              </button>
            </div>
          </div>
        )}

        {/* Control Buttons */}
        <div className="flex space-x-3 pt-4">
          {!messengerStatus.isRunning ? (
            <button
              onClick={handleStartMessenger}
              disabled={isStarting || !isConnected}
              className="btn-primary flex items-center"
            >
              {isStarting ? (
                <div className="spinner mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              {isStarting ? 'Đang bắt đầu...' : 'Bắt đầu Messenger'}
            </button>
          ) : (
            <button
              onClick={handleStopMessenger}
              disabled={isStopping}
              className="btn-danger flex items-center"
            >
              {isStopping ? (
                <div className="spinner mr-2" />
              ) : (
                <Square className="h-4 w-4 mr-2" />
              )}
              {isStopping ? 'Đang dừng...' : 'Dừng Messenger'}
            </button>
          )}

          <button
            onClick={() => {
              loadSavedCookies();
              loadMessengerStatus();
            }}
            className="btn-secondary flex items-center"
            disabled={isStarting || isStopping}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </button>

        </div>

        {/* Stats */}
        {messengerStatus.isRunning && (
          <div className="bg-gray-50 rounded-lg p-4 mt-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Thống kê</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Hàng đợi:</span>
                <div className="font-semibold text-gray-900">{messengerStatus.queueLength}</div>
              </div>
              <div>
                <span className="text-gray-500">Trạng thái:</span>
                <div className="font-semibold text-gray-900">
                  {messengerStatus.isProcessingQueue ? 'Đang xử lý' : 'Chờ'}
                </div>
              </div>
              <div>
                <span className="text-gray-500">Kết nối:</span>
                <div className="font-semibold text-gray-900">
                  {messengerStatus.browserConnected ? 'Đã kết nối' : 'Chưa kết nối'}
                </div>
              </div>
              {messengerStatus.messagesProcessed !== undefined && (
                <div>
                  <span className="text-gray-500">Tin nhắn đã gửi:</span>
                  <div className="font-semibold text-gray-900">
                    {messengerStatus.messagesProcessed}/{messengerStatus.maxMessagesBeforeRestart}
                  </div>
                </div>
              )}
              {messengerStatus.lastRestartTime && (
                <div>
                  <span className="text-gray-500">Restart cuối:</span>
                  <div className="font-semibold text-gray-900">
                    {new Date(messengerStatus.lastRestartTime).toLocaleTimeString('vi-VN')}
                  </div>
                </div>
              )}
            </div>

            {/* Performance warning */}
            {messengerStatus.messagesProcessed >= (messengerStatus.maxMessagesBeforeRestart * 0.8) && (
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center">
                  <div className="text-yellow-600 text-sm">
                    ⚠️ Sắp restart tự động sau {messengerStatus.maxMessagesBeforeRestart - messengerStatus.messagesProcessed} tin nhắn nữa
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Security Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Lưu ý bảo mật</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  Messenger sẽ mở Chrome riêng biệt và tự động đăng nhập. Cookies đăng nhập được lưu trữ cục bộ để tiện lợi.
                  Sau khi đăng nhập thành công, bạn có thể sử dụng nút "Xóa cookies" để xóa phiên đăng nhập.
                  Khuyến nghị sử dụng tài khoản khác với tài khoản scraper để đảm bảo an toàn.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Login Popup */}
      <LoginPopup
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        onLogin={handlePopupLogin}
        title="Đăng nhập Instagram - Tin nhắn tự động"
        isLoading={isStarting}
      />
    </div>
  );
};

export default MessengerControl;
