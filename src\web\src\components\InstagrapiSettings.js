import React, { useState, useEffect } from 'react';
import { useSocket } from '../contexts/SocketContext';
import { showToast } from '../utils/toastManager';

const InstagrapiSettings = () => {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [sessionId, setSessionId] = useState('');
    const [useSessionId, setUseSessionId] = useState(false);
    const [status, setStatus] = useState('idle');
    const [isRunning, setIsRunning] = useState(false);
    const socket = useSocket();

    useEffect(() => {
        checkStatus();

        // Kiểm tra status mỗi 30 giây
        const interval = setInterval(() => {
            checkStatus();
        }, 30000);

        return () => clearInterval(interval);
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    const checkStatus = async () => {
        try {
            const response = await fetch('/api/messenger/instagrapi/status');
            const data = await response.json();

            if (data.success) {
                setIsRunning(data.status.isRunning);
                setStatus(data.status.isLoggedIn ? 'connected' : 'idle');
            }
        } catch (error) {
            console.error('Failed to check instagrapi status:', error);
        }
    };

    const handleLogin = async () => {
        setStatus('connecting');

        try {
            let response;

            if (useSessionId) {
                if (!sessionId) {
                    showToast('Vui lòng nhập Session ID', 'error');
                    setStatus('idle');
                    return;
                }

                response = await fetch('/api/messenger/instagrapi/login-by-session', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sessionId })
                });
            } else {
                if (!username || !password) {
                    showToast('Vui lòng nhập đầy đủ tên đăng nhập và mật khẩu', 'error');
                    setStatus('idle');
                    return;
                }

                response = await fetch('/api/messenger/instagrapi/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
            }

            const data = await response.json();

            if (data.success) {
                setStatus('connected');
                showToast('Đăng nhập Instagram thành công', 'success');

                // Bắt đầu queue processor sau khi đăng nhập thành công
                await startQueueProcessor();
            } else {
                setStatus('error');
                showToast(`Đăng nhập thất bại: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            setStatus('error');
            showToast(`Lỗi đăng nhập: ${error.message}`, 'error');
        }
    };

    const startQueueProcessor = async () => {
        try {
            const response = await fetch('/api/messenger/instagrapi/start', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                setIsRunning(true);
                showToast('Bắt đầu xử lý hàng đợi tin nhắn', 'success');
            } else {
                showToast(`Không thể bắt đầu xử lý: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Start queue processor error:', error);
            showToast(`Lỗi bắt đầu xử lý: ${error.message}`, 'error');
        }
    };

    const stopQueueProcessor = async () => {
        try {
            const response = await fetch('/api/messenger/instagrapi/stop', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                setIsRunning(false);
                showToast('Đã dừng xử lý hàng đợi tin nhắn', 'success');
            } else {
                showToast(`Không thể dừng xử lý: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('Stop queue processor error:', error);
            showToast(`Lỗi dừng xử lý: ${error.message}`, 'error');
        }
    };

    return (
        <div className="bg-white p-4 rounded-lg shadow mb-4">
            <h2 className="text-lg font-bold mb-4">Cài đặt Instagrapi API</h2>

            <div className="mb-4">
                <label className="flex items-center space-x-2 mb-2">
                    <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600"
                        checked={useSessionId}
                        onChange={() => setUseSessionId(!useSessionId)}
                        disabled={status === 'connected' || status === 'connecting'}
                    />
                    <span className="text-sm">Sử dụng Session ID thay vì đăng nhập</span>
                </label>
            </div>

            {!useSessionId ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Tên đăng nhập Instagram
                        </label>
                        <input
                            type="text"
                            className="border rounded w-full p-2"
                            placeholder="Nhập tên đăng nhập"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            disabled={status === 'connected' || status === 'connecting'}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Mật khẩu
                        </label>
                        <input
                            type="password"
                            className="border rounded w-full p-2"
                            placeholder="Nhập mật khẩu"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            disabled={status === 'connected' || status === 'connecting'}
                        />
                    </div>
                </div>
            ) : (
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Instagram Session ID
                    </label>
                    <input
                        type="text"
                        className="border rounded w-full p-2"
                        placeholder="Nhập Session ID từ Instagram"
                        value={sessionId}
                        onChange={(e) => setSessionId(e.target.value)}
                        disabled={status === 'connected' || status === 'connecting'}
                    />
                </div>
            )}

            <div className="flex space-x-3 mb-3">
                {status !== 'connected' ? (
                    <button
                        onClick={handleLogin}
                        disabled={status === 'connecting'}
                        className={`px-4 py-2 rounded ${status === 'connecting' ? 'bg-yellow-500' : 'bg-green-500'
                            } text-white`}
                    >
                        {status === 'connecting' ? 'Đang kết nối...' : 'Kết nối'}
                    </button>
                ) : (
                    <div className="flex space-x-2">
                        {isRunning ? (
                            <button
                                onClick={stopQueueProcessor}
                                className="px-4 py-2 rounded bg-red-500 text-white"
                            >
                                Dừng xử lý
                            </button>
                        ) : (
                            <button
                                onClick={startQueueProcessor}
                                className="px-4 py-2 rounded bg-green-500 text-white"
                            >
                                Bắt đầu xử lý
                            </button>
                        )}
                    </div>
                )}
            </div>

            <div className="mt-3">
                <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${status === 'idle' ? 'bg-gray-400' :
                        status === 'connecting' ? 'bg-yellow-500' :
                            status === 'connected' ? 'bg-green-500' :
                                'bg-red-500'
                        }`}></div>
                    <span className="text-sm text-gray-600">
                        {status === 'idle' ? 'Chưa kết nối' :
                            status === 'connecting' ? 'Đang kết nối...' :
                                status === 'connected' ? 'Đã kết nối' :
                                    'Lỗi kết nối'}
                    </span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                    <div className={`w-3 h-3 rounded-full ${isRunning ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span className="text-sm text-gray-600">
                        {isRunning ? 'Đang xử lý hàng đợi' : 'Chưa xử lý hàng đợi'}
                        {isRunning && ' (10 giây/tin nhắn)'}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default InstagrapiSettings; 