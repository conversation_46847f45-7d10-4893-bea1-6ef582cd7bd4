import React, { createContext, useContext, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { showMessageToast, showNewCommentToast, showErrorToast, showToast } from '../utils/toastManager';
import { getApiUrl } from '../config/api';

// Removed cleanup configuration - system can handle unlimited comments

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children, socket, isConnected }) => {
  const [systemState, setSystemState] = useState({
    isRunning: false,
    connectedClients: 0,
    totalComments: 0,
    processedComments: 0,
    queuedMessages: 0,
    sentMessages: 0,
    printedComments: 0,
    errors: 0,
    startTime: null
  });

  const [recentComments, setRecentComments] = useState([]);
  // Thêm state mới để theo dõi số lượng comments hiển thị
  const [displayedComments, setDisplayedComments] = useState([]);
  const [commentsPerPage, setCommentsPerPage] = useState(100);
  const [queueStats, setQueueStats] = useState({
    waiting: 0,
    active: 0,
    completed: 0,
    failed: 0
  });

  // Load session comments on mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('=== LOADING INITIAL DATA ON PAGE REFRESH ===');

        // Load session comments
        const commentsResponse = await fetch(getApiUrl('/api/session-comments'));
        const commentsData = await commentsResponse.json();
        if (commentsData.success) {
          console.log('=== LOADED SESSION COMMENTS ===');
          console.log('Comments count:', commentsData.comments.length);
          console.log('Total comments from server:', commentsData.total);

          if (commentsData.comments.length > 0) {
            // Sắp xếp bình luận theo thời gian tăng dần (cũ nhất lên đầu)
            const sortedComments = commentsData.comments.sort((a, b) => {
              const timeA = new Date(a.timestamp).getTime();
              const timeB = new Date(b.timestamp).getTime();
              return timeA - timeB;
            });

            setRecentComments(sortedComments);

            // Chỉ hiển thị 100 comments mới nhất ban đầu
            const latestComments = sortedComments.length > commentsPerPage
              ? sortedComments.slice(-commentsPerPage)
              : sortedComments;

            setDisplayedComments(latestComments);

            console.log('✅ Session comments restored after page refresh');
            console.log(`✅ Displaying ${latestComments.length} latest comments initially`);

            // Nếu có quá nhiều comments, hiển thị thông báo
            if (sortedComments.length > commentsPerPage) {
              console.log(`📌 ${sortedComments.length - commentsPerPage} older comments can be loaded by scrolling up`);
            }
          } else {
            console.log('ℹ️ No session comments to restore');
          }
        } else {
          console.error('Failed to load session comments:', commentsData.error);
        }

        // Load session-based system state (chỉ trong phiên hiện tại)
        console.log('=== LOADING SESSION SYSTEM STATE ===');
        const statsResponse = await fetch(getApiUrl('/api/stats'));
        const statsData = await statsResponse.json();
        if (statsData) {
          console.log('=== LOADED SESSION SYSTEM STATE ===');
          console.log('Session stats data:', statsData);
          setSystemState(prevState => ({
            ...prevState,
            ...statsData,
            totalComments: commentsData.comments?.length || statsData.totalComments || 0
          }));
          console.log('✅ Session system state loaded (session-based counters only)');
        }

        // Load queue stats chỉ để hiển thị trong queue manager, không override session counters
        console.log('=== LOADING QUEUE STATS (for queue manager only) ===');
        const queueResponse = await fetch(getApiUrl('/api/message-queue?status=all'));
        const queueData = await queueResponse.json();
        if (queueData.success) {
          const queueStats = {
            waiting: queueData.messages.filter(m => m.status === 'pending').length,
            active: queueData.messages.filter(m => m.status === 'processing').length,
            completed: queueData.messages.filter(m => m.status === 'completed').length,
            failed: queueData.messages.filter(m => m.status === 'failed').length
          };
          console.log('=== LOADED QUEUE STATS (for reference only) ===');
          console.log('Queue stats:', queueStats);
          setQueueStats(queueStats);
          // KHÔNG override systemState.queuedMessages - giữ session counter
          console.log('✅ Queue stats loaded (session counters preserved)');
        }

      } catch (error) {
        console.error('Failed to load initial data:', error);
      }
    };

    loadInitialData();
  }, [commentsPerPage]);

  // Không cần cập nhật toàn bộ displayedComments khi recentComments thay đổi
  // Chúng ta chỉ cần thêm comment mới vào cuối danh sách (xử lý trong socket.on('new-comment'))

  // Hàm để load thêm comments cũ
  const loadMoreComments = () => {
    // Kiểm tra xem có đủ comments để tải thêm không (ít nhất 100 comments)
    if (recentComments.length < commentsPerPage) {
      console.log(`⚠️ Không đủ comments để load thêm. Hiện có ${recentComments.length} comments, cần ít nhất ${commentsPerPage}`);
      return false;
    }

    // Kiểm tra xem đã hiển thị tất cả comments chưa
    if (recentComments.length <= displayedComments.length) {
      console.log('⚠️ No more comments to load, all comments are displayed');
      return false;
    }

    // Tìm vị trí của comment cũ nhất đang hiển thị trong danh sách đầy đủ
    const oldestDisplayedComment = displayedComments[0];
    const currentOldestIndex = recentComments.findIndex(
      comment => comment.id === oldestDisplayedComment.id
    );

    // Nếu không tìm thấy (hiếm khi xảy ra) hoặc đã ở đầu danh sách
    if (currentOldestIndex <= 0) {
      console.log('⚠️ Already at the beginning of comment list');
      return false;
    }

    // Tính số lượng comments cũ hơn cần load thêm
    const batchSize = 50; // Load thêm 50 comments mỗi lần
    const startIndex = Math.max(0, currentOldestIndex - batchSize);
    const additionalComments = recentComments.slice(startIndex, currentOldestIndex);

    if (additionalComments.length > 0) {
      setDisplayedComments(prevDisplayed => [...additionalComments, ...prevDisplayed]);
      console.log(`✅ Loaded ${additionalComments.length} older comments, from index ${startIndex} to ${currentOldestIndex - 1}`);
      return true;
    } else {
      console.log('⚠️ No additional comments to load');
      return false;
    }
  };

  // Sync totalComments with recentComments.length (debounced to prevent rapid updates)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSystemState(prev => ({
        ...prev,
        totalComments: recentComments.length
      }));
    }, 100); // 100ms debounce

    return () => clearTimeout(timeoutId);
  }, [recentComments.length]);

  useEffect(() => {
    if (!socket) return;

    console.log('=== SOCKET CONTEXT: Setting up event listeners ===');
    console.log('Socket ID:', socket.id);
    console.log('Socket connected:', socket.connected);

    // System state updates (but preserve client-side totalComments if more accurate)
    socket.on('system-state', (state) => {
      console.log('Received system-state:', state);
      setSystemState(prevState => ({
        ...state,
        // Use client-side totalComments if it's more accurate (based on actual displayed comments)
        totalComments: recentComments.length > 0 ? recentComments.length : state.totalComments
      }));
    });

    // New comment received
    socket.on('new-comment', (comment) => {
      console.log('=== WEB RECEIVED NEW COMMENT ===');
      console.log('Comment:', comment);
      console.log('Username:', comment.username);
      console.log('Text:', comment.text);
      console.log('ID:', comment.id);
      console.log('Timestamp:', comment.timestamp);

      setRecentComments(prev => {
        // Add new comment and sort by timestamp
        const updated = [...prev, comment];

        // Sắp xếp bình luận theo thời gian tăng dần (cũ nhất lên đầu)
        const sorted = updated.sort((a, b) => {
          const timeA = new Date(a.timestamp).getTime();
          const timeB = new Date(b.timestamp).getTime();
          return timeA - timeB;
        });

        // Log status every 100 comments for monitoring
        if (sorted.length % 100 === 0) {
          console.log(`📱 Client comments status: ${sorted.length} total comments`);
        }

        return sorted;
      });

      // Thêm comment mới vào cuối danh sách hiển thị
      setDisplayedComments(prev => {
        // Thêm comment mới vào và giữ nguyên thứ tự
        const updated = [...prev, comment];

        // Sắp xếp lại để đảm bảo đúng thứ tự thời gian
        const sorted = updated.sort((a, b) => {
          const timeA = new Date(a.timestamp).getTime();
          const timeB = new Date(b.timestamp).getTime();
          return timeA - timeB;
        });

        return sorted;
      });

      // Don't manually update totalComments here - let the useEffect handle it
      // This prevents race conditions and duplicate updates

      // Show notification for new comment (managed by toast manager)
      showNewCommentToast(`Bình luận mới từ @${comment.username}`);

      console.log('=== COMMENT PROCESSED IN WEB ===');
    });

    // Duplicate comment detected
    socket.on('duplicate-comment', (comment) => {
      console.log('Duplicate comment filtered:', comment);
    });

    // Queue statistics updated
    socket.on('queue-stats', (stats) => {
      setQueueStats(stats);
      setSystemState(prev => ({
        ...prev,
        queuedMessages: stats.waiting
      }));
    });

    // Message sent successfully
    socket.on('message-sent', (data) => {
      // ✅ DO NOT update sentMessages here - server will send updated system-state
      // This prevents duplicate counter updates

      // Use toast manager for message toasts
      showMessageToast(`Tin nhắn đã gửi đến @${data.username}`);
    });

    // Message failed to send
    socket.on('message-failed', (data) => {
      // ✅ DO NOT update errors here - server will send updated system-state
      // This prevents duplicate counter updates

      showErrorToast(`Lỗi gửi tin nhắn đến @${data.username}`, { duration: 5000 });
    });

    // Scraper connected
    socket.on('scraper-connected', () => {
      setSystemState(prev => ({
        ...prev,
        isRunning: true,
        startTime: new Date().toISOString()
      }));

      toast.success('Đã kết nối với Instagram Live', {
        duration: 4000,
        icon: '🔗'
      });
    });

    // Scraper disconnected
    socket.on('scraper-disconnected', () => {
      setSystemState(prev => ({
        ...prev,
        isRunning: false
      }));

      toast.error('Mất kết nối với Instagram Live', {
        duration: 4000,
        icon: '🔌'
      });
    });

    // Scraper error
    socket.on('scraper-error', (data) => {
      // ✅ DO NOT update errors here - server will send updated system-state
      // This prevents duplicate counter updates

      toast.error(`Lỗi scraper: ${data.error}`, {
        duration: 5000,
        icon: '⚠️'
      });
    });

    // Order detected
    socket.on('order-detected', (data) => {
      toast.success(`Phát hiện đơn hàng từ @${data.comment.username}`, {
        duration: 4000,
        icon: '🛒'
      });
    });

    // Price inquiry detected
    socket.on('price-inquiry-detected', (data) => {
      toast.info(`Câu hỏi về giá từ @${data.comment.username}`, {
        duration: 3000,
        icon: '💰'
      });
    });

    // Manual order created
    socket.on('manual-order-created', (data) => {
      toast.success(`Đơn hàng đã tạo cho @${data.comment.username}`, {
        duration: 4000,
        icon: '📝'
      });
    });

    // Custom message queued
    socket.on('custom-message-queued', (data) => {
      toast.info(`Tin nhắn đã thêm vào hàng đợi cho @${data.comment.username}`, {
        duration: 3000,
        icon: '📤'
      });
    });

    // Messenger 2FA required
    socket.on('messenger-2fa-required', (data) => {
      toast.error('Cần xác thực 2FA cho tin nhắn tự động', {
        duration: 10000,
        icon: '🔐'
      });
      console.log('2FA required for messenger:', data);
    });

    // Print success
    socket.on('print-success', (data) => {
      // ✅ DO NOT update printedComments here - server will send updated system-state
      // This prevents duplicate counter updates on the client that initiated the print

      // Toast is now handled in PrintButton.js to avoid duplicates
      // toast.success('In thành công', {
      //   duration: 3000,
      //   icon: '🖨️'
      // });
    });

    // Print error
    socket.on('print-error', (data) => {
      showErrorToast(`Lỗi in: ${data.error}`, { duration: 4000 });
    });

    // Clear processed comments success
    socket.on('clear-processed-comments-success', (data) => {
      showToast('Đã xóa bộ nhớ comment - sẽ hiển thị lại tất cả comment', 'success', {
        duration: 4000,
        icon: '🗑️'
      });
    });

    // Clear processed comments error
    socket.on('clear-processed-comments-error', (data) => {
      toast.error(`Lỗi xóa bộ nhớ comment: ${data.error}`, {
        duration: 4000,
        icon: '❌'
      });
    });

    // Socket error handling
    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
    });

    socket.on('disconnect', (reason) => {
      console.warn('Socket disconnected:', reason);
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    // Test if socket is receiving any events
    socket.onAny((eventName, ...args) => {
      console.log(`=== SOCKET EVENT RECEIVED: ${eventName} ===`, args);
    });

    console.log('=== SOCKET CONTEXT: All event listeners set up ===');

    // Cleanup listeners on unmount
    return () => {
      console.log('=== SOCKET CONTEXT: Cleaning up event listeners ===');
      socket.off('system-state');
      socket.off('new-comment');
      socket.off('comment-processed');
      socket.off('duplicate-comment');
      socket.off('queue-stats');
      socket.off('message-sent');
      socket.off('message-failed');
      socket.off('scraper-connected');
      socket.off('scraper-disconnected');
      socket.off('scraper-error');
      socket.off('order-detected');
      socket.off('price-inquiry-detected');
      socket.off('manual-order-created');
      socket.off('custom-message-queued');
      socket.off('print-success');
      socket.off('print-error');
      socket.off('clear-processed-comments-success');
      socket.off('clear-processed-comments-error');
      socket.off('connect_error');
      socket.off('disconnect');
      socket.off('error');
      socket.offAny();
    };
  }, [socket]);

  // Socket utility functions
  const sendMessage = (data) => {
    if (socket && isConnected) {
      socket.emit('send-message', data);
    } else {
      toast.error('Không có kết nối với server');
    }
  };

  const printComment = (comment, templateType = 'print_notification') => {
    return new Promise((resolve, reject) => {
      if (!socket || !isConnected) {
        reject(new Error('Không có kết nối với server'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Timeout: Không nhận được phản hồi từ server'));
      }, 30000); // 30 second timeout

      const handleSuccess = (data) => {
        // Only handle if this is for the same comment
        if (data.commentId === comment.id) {
          clearTimeout(timeout);
          socket.off('print-success', handleSuccess);
          socket.off('print-error', handleError);
          resolve(data);
        }
      };

      const handleError = (error) => {
        // Only handle if this is for the same comment
        if (error.commentId === comment.id) {
          clearTimeout(timeout);
          socket.off('print-success', handleSuccess);
          socket.off('print-error', handleError);
          reject(new Error(error.error || 'Lỗi không xác định'));
        }
      };

      socket.on('print-success', handleSuccess);
      socket.on('print-error', handleError);

      socket.emit('print-comment', comment, templateType);
    });
  };

  const requestSystemState = () => {
    if (socket && isConnected) {
      socket.emit('get-system-state');
    }
  };

  const clearProcessedComments = () => {
    if (socket && isConnected) {
      socket.emit('clear-processed-comments');
    } else {
      toast.error('Không có kết nối với server');
    }
  };

  const clearSessionComments = async () => {
    try {
      const response = await fetch(getApiUrl('/api/clear-session-comments'), {
        method: 'POST'
      });
      const data = await response.json();
      if (data.success) {
        setRecentComments([]);
        setDisplayedComments([]);
        toast.success('Đã xóa tất cả comments trong phiên');
      }
    } catch (error) {
      console.error('Failed to clear session comments:', error);
      toast.error('Lỗi khi xóa comments');
    }
  };

  const value = {
    socket,
    isConnected,
    systemState,
    recentComments,
    displayedComments,
    loadMoreComments,
    setCommentsPerPage,
    queueStats,
    sendMessage,
    printComment,
    requestSystemState,
    clearProcessedComments,
    clearSessionComments,
    setRecentComments,
    setSystemState
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
