const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const winston = require('winston');
const axios = require('axios');
require('dotenv').config();

// Import custom modules
const InstagramScraper = require('./services/InstagramScraper');
const InstagramMessenger = require('./services/InstagramMessenger');
const InstagrapiMessenger = require('./services/InstagrapiMessenger');
const MessageQueue = require('./services/MessageQueue');
const Database = require('./services/Database');
const CommentProcessor = require('./services/CommentProcessor');
const PrinterService = require('./services/PrinterService');
const MongoDBService = require('./services/MongoDBService');

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'instagram-live-system' },
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: false
}));
app.use(cors({
  origin: true, // Allow all origins for mobile access
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Request logging middleware
app.use('/api', (req, res, next) => {
  logger.info(`API Request: ${req.method} ${req.path}`);
  next();
});

// Image proxy route to bypass CORS restrictions
app.get('/api/proxy-image', async (req, res) => {
  const imageUrl = req.query.url;

  if (!imageUrl) {
    return res.status(400).send('URL parameter is required');
  }

  try {
    // Validate the URL is from Instagram domains for security
    const validDomains = [
      'instagram.com',
      'cdninstagram.com',
      'fbcdn.net'
    ];

    const url = new URL(imageUrl);
    const isValidDomain = validDomains.some(domain => url.hostname.includes(domain));

    if (!isValidDomain) {
      return res.status(403).send('Only Instagram image URLs are allowed');
    }

    // Fetch the image
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    // Set content type
    const contentType = response.headers['content-type'];
    res.setHeader('Content-Type', contentType);

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');

    // Send image data
    res.send(response.data);

  } catch (error) {
    logger.error(`Error proxying image: ${error.message}`);
    res.status(500).send('Error fetching image');
  }
});

// Serve static files for web interface
app.use('/web', express.static(path.join(__dirname, '../web/build')));

// Initialize services (variables declared in global section below)

// System state
let systemState = {
  isRunning: false,
  connectedClients: 0,
  totalComments: 0,
  queuedMessages: 0,
  sentMessages: 0,
  printedComments: 0,
  errors: 0,
  startTime: null
};

// In-memory comment cache for current session - khởi tạo là biến global để có thể truy cập từ các module khác
global.sessionComments = [];
let isScrapingActive = false;

// System state emission optimization
let lastSystemStateEmission = 0;
const SYSTEM_STATE_DEBOUNCE_MS = 1000; // Minimum 1 second between system state emissions

// Debounced system state emission
function emitSystemStateDebounced() {
  const now = Date.now();
  if (now - lastSystemStateEmission >= SYSTEM_STATE_DEBOUNCE_MS) {
    lastSystemStateEmission = now;
    io.emit('system-state', systemState);
  }
}

// Global service variables
let database;
let commentProcessor;
let instagramScraper;
let instagramMessenger;
let instagrapiMessenger; // Thêm biến cho InstagrapiMessenger
let messageQueue;
let printerService;
let mongoDBService;

// Removed all cleanup configuration - system handles unlimited comments

// MongoDB sync configuration
const MONGODB_SYNC_CONFIG = {
  AUTO_SYNC_INTERVAL: 300000, // Auto sync mỗi 5 phút (300000ms)
  ENABLED: false
};

// Removed all cleanup functions - no longer needed

// Initialize services
async function initializeServices() {
  try {
    logger.info('Initializing services...');

    // Initialize database
    database = new Database();
    await database.initialize();

    // Initialize message queue
    messageQueue = new MessageQueue();
    await messageQueue.initialize();

    // Initialize comment processor
    commentProcessor = new CommentProcessor(database);

    // Initialize Instagram scraper
    instagramScraper = new InstagramScraper();

    // Initialize Instagram messenger
    instagramMessenger = new InstagramMessenger(database);

    // Initialize Instagrapi messenger
    instagrapiMessenger = new InstagrapiMessenger();

    // Set instagrapiMessenger vào messageQueue
    messageQueue.setInstagrapiMessenger(instagrapiMessenger);

    // Initialize printer service
    printerService = new PrinterService(logger, database);
    await printerService.initialize();

    // Initialize MongoDB service (optional - will be configured via settings)
    mongoDBService = new MongoDBService();

    // Make mongoDBService globally available for auto-sync
    global.mongoDBService = mongoDBService;

    // Đặt instagrapiMessenger vào biến global để sử dụng từ các service khác
    global.instagrapiMessenger = instagrapiMessenger;

    // Try to auto-connect to MongoDB if connection string exists
    // await tryAutoConnectMongoDB();

    // Set up event listeners
    setupEventListeners();

    // Setup periodic MongoDB sync
    // setupMongoDBAutoSync();

    // Setup automatic send_once history cleanup
    setupSendOnceHistoryCleanup();


    // Setup automatic sent messages cleanup
    setupSentMessagesCleanup();

    // Khởi động và đăng nhập tự động dịch vụ InstagrapiMessenger nếu có session
    try {
      logger.info('Đang kiểm tra phiên đăng nhập Instagrapi...');

      // Thử kết nối tới dịch vụ InstagrapiService
      const instagrapiStatus = await axios.get('http://localhost:5000/status', { timeout: 2000 });

      if (instagrapiStatus.data && instagrapiStatus.data.is_logged_in) {
        logger.info('✅ InstagrapiService đã đăng nhập sẵn, không cần đăng nhập lại');
        await instagrapiMessenger.startQueueProcessor();
        logger.info('✅ Đã khởi động queue processor của Instagrapi');

        // Đặt chế độ gửi tin nhắn mặc định là dùng Instagrapi API
        if (messageQueue) {
          messageQueue.setUseInstagrapi(true);
          logger.info('✅ Đã đặt chế độ mặc định là sử dụng Instagrapi API');
        }
      } else {
        logger.info('⚠️ InstagrapiService chưa đăng nhập, cần đăng nhập thủ công');
      }
    } catch (error) {
      logger.warn('❌ Không thể kết nối tới InstagrapiService:', error.message);
      logger.warn('⚠️ Vui lòng khởi động InstagrapiService và đăng nhập thủ công');
    }

    logger.info('All services initialized successfully');
    return true;
  } catch (error) {
    logger.error('Failed to initialize services:', error);
    throw error;
  }
}

// Try to auto-connect to MongoDB on startup
async function tryAutoConnectMongoDB() {
  try {
    const connectionString = await database.getSetting('mongodb_connection_string');
    if (connectionString) {
      logger.info('Found saved MongoDB connection string, attempting to reconnect...');
      await mongoDBService.connect(connectionString);
      logger.info('MongoDB auto-reconnected successfully');

      // Chức năng tự động đồng bộ MongoDB khi khởi động đã bị vô hiệu hóa
      // await performStartupSync();
      logger.info('Chức năng tự động đồng bộ MongoDB khi khởi động đã bị vô hiệu hóa');
      logger.info('Sử dụng nút đồng bộ thủ công trong trang cài đặt để đồng bộ khi cần');
    }
  } catch (error) {
    logger.warn('Failed to auto-reconnect to MongoDB:', error.message);
    // Don't throw error - MongoDB is optional
  }
}

// Perform smart bidirectional sync on startup
async function performStartupSync() {
  try {
    if (mongoDBService && mongoDBService.isConnected) {
      logger.info('Performing startup sync with MongoDB...');

      // Get customers from MongoDB
      const mongoCustomers = await mongoDBService.syncCustomersFromMongo();

      // Get threads from MongoDB
      const mongoThreads = await mongoDBService.syncThreadsFromMongo();

      // Get send_once history from MongoDB
      const mongoSendOnceHistory = await mongoDBService.syncSendOnceHistoryFromMongo();

      // Perform smart sync for customers
      const customerSyncResult = await database.smartSyncWithMongoDB(mongoCustomers);

      // Perform smart sync for threads
      const threadSyncResult = await database.smartSyncThreadsWithMongoDB(mongoThreads);

      // Get printed history from MongoDB
      const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();

      // Perform smart sync for printed history (consistent with manual/periodic sync)
      const printedHistorySyncResult = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory);

      // Perform smart sync for send_once history
      const sendOnceHistorySyncResult = await database.smartSyncSendOnceHistoryWithMongoDB(mongoSendOnceHistory);

      const combinedResult = {
        customers: customerSyncResult,
        threads: threadSyncResult,
        printedHistory: printedHistorySyncResult,
        sendOnceHistory: sendOnceHistorySyncResult
      };

      logger.info('Startup sync completed:', combinedResult);

      // Save last sync time
      await database.saveSetting('mongodb_last_sync', new Date().toISOString());

      return combinedResult;
    }
  } catch (error) {
    logger.error('Startup sync failed:', error);
    // Don't throw error - sync failure shouldn't prevent app startup
  }
}

// Setup event listeners
function setupEventListeners() {
  // Instagram scraper events
  instagramScraper.on('comment', (comment) => {
    logger.info(`=== SERVER RECEIVED COMMENT ===`);
    logger.info(`From: ${comment.username}`);
    logger.info(`Text: ${comment.text}`);
    logger.info(`ID: ${comment.id}`);
    logger.info(`Timestamp: ${comment.timestamp}`);
    logger.info(`Connected clients: ${systemState.connectedClients}`);

    // Add to session cache (always add, no cleanup needed)
    // Không ghi đè timestamp vì đã được lưu từ created_at trong API
    global.sessionComments.push({
      ...comment,
      sessionId: Date.now()
    });

    // Update system state with current comment count
    systemState.totalComments = global.sessionComments.length;

    // Log status every 100 comments for monitoring
    if (global.sessionComments.length % 100 === 0) {
      logger.info(`📊 Session status: ${global.sessionComments.length} comments, API monitoring: ${isScrapingActive}`);
    }

    // Process comment
    commentProcessor.processComment(comment);

    // Emit comment to all connected clients
    io.emit('new-comment', comment);

    // Emit system state with debouncing to prevent spam
    emitSystemStateDebounced();

    logger.info(`=== COMMENT EMITTED TO ${systemState.connectedClients} CLIENTS ===`);
  });

  instagramScraper.on('error', (error) => {
    systemState.errors++;
    logger.error('Instagram scraper error:', error);
    io.emit('scraper-error', { error: error.message });
  });

  instagramScraper.on('connected', () => {
    logger.info('Instagram scraper connected to live stream');
    isScrapingActive = true;
    io.emit('scraper-connected');
  });

  instagramScraper.on('disconnected', () => {
    logger.warn('Instagram scraper disconnected from live stream');
    isScrapingActive = false;
    io.emit('scraper-disconnected');
  });

  // Handle cookies from successful login
  instagramScraper.on('cookies-available', async (data) => {
    try {
      await database.saveCookies(data.username, data.cookies, 'scraper');
      logger.info(`Scraper cookies saved for user: ${data.username}`);
    } catch (error) {
      logger.error('Failed to save scraper cookies:', error);
    }
  });

  // Handle cookies from successful messenger login
  instagramMessenger.on('cookies-available', async (data) => {
    try {
      await database.saveMessengerCookies(data.username, data.cookies);
      logger.info(`Messenger cookies saved for user: ${data.username}`);

      // Emit to frontend that cookies are now available
      io.emit('messenger-cookies-saved', { username: data.username });
    } catch (error) {
      logger.error('Failed to save messenger cookies:', error);
    }
  });

  // Forward messenger events to socket clients
  instagramMessenger.on('messenger-connected', () => {
    io.emit('messenger-connected');
  });

  instagramMessenger.on('messenger-disconnected', () => {
    io.emit('messenger-disconnected');
  });

  instagramMessenger.on('message-queued', (data) => {
    systemState.queuedMessages++;
    io.emit('message-queued', data);
    io.emit('system-state', systemState); // Emit updated system state
  });

  instagramMessenger.on('messageStatusUpdate', (data) => {
    io.emit('message-status-update', data);
  });

  instagramMessenger.on('messageVerificationFailed', (data) => {
    logger.warn(`Message verification failed for @${data.username}: ${data.error}`);
    io.emit('message-verification-failed', data);
  });

  instagramMessenger.on('2fa-required', (data) => {
    io.emit('messenger-2fa-required', data);
  });

  instagramMessenger.on('message-sent', (data) => {
    const oldSent = systemState.sentMessages;
    const oldQueued = systemState.queuedMessages;
    systemState.sentMessages++;
    systemState.queuedMessages = Math.max(0, systemState.queuedMessages - 1); // Decrease queue count
    logger.info(`📊 MESSAGE COUNTER UPDATE: sentMessages ${oldSent} → ${systemState.sentMessages}, queuedMessages ${oldQueued} → ${systemState.queuedMessages}`);
    logger.info(`Message sent to ${data.username}`);
    io.emit('message-sent', data);
    io.emit('system-state', systemState); // Emit updated system state
  });

  instagramMessenger.on('messaging-start', (data) => {
    logger.info(`Started messaging to ${data.username}`);
    io.emit('messaging-start', data);
  });

  instagramMessenger.on('messaging-complete', (data) => {
    logger.info(`Completed messaging to ${data.username} - Success: ${data.success}`);
    io.emit('messaging-complete', data);
  });

  // Message queue events - removed duplicate message-sent handler
  // InstagramMessenger already handles message-sent events

  messageQueue.on('message-failed', (data) => {
    systemState.errors++;
    logger.error(`Failed to send message to ${data.username}:`, data.error);
    io.emit('message-failed', data);
    io.emit('system-state', systemState); // Emit updated system state
  });

  messageQueue.on('queue-updated', (stats) => {
    systemState.queuedMessages = stats.waiting;
    io.emit('queue-stats', stats);
  });

  // Comment processor events

  commentProcessor.on('duplicate-comment', (comment) => {
    logger.debug(`Duplicate comment filtered: ${comment.id}`);
    io.emit('duplicate-comment', comment);
  });

  // Instagram messenger events
  instagramMessenger.on('connected', () => {
    logger.info('Instagram Messenger connected');
    io.emit('messenger-connected');
  });

  instagramMessenger.on('disconnected', () => {
    logger.warn('Instagram Messenger disconnected');
    io.emit('messenger-disconnected');
  });

  instagramMessenger.on('messageQueued', (messageData) => {
    logger.info(`Message queued for ${messageData.username}`);
    io.emit('message-queued', messageData);
  });

  instagramMessenger.on('messageStatusUpdate', (messageData) => {
    logger.info(`Message status updated: ${messageData.username} - ${messageData.status}`);
    io.emit('message-status-update', messageData);
  });

  // Browser restart events
  instagramMessenger.on('browser-restarted', (data) => {
    logger.info(`Browser restarted successfully - Messages processed: ${data.messagesProcessed}, Memory: ${data.memoryUsage}MB`);
    io.emit('messenger-browser-restarted', data);
  });

  instagramMessenger.on('restart-failed', (data) => {
    logger.error(`Browser restart failed: ${data.error}`);
    io.emit('messenger-restart-failed', data);
  });

  // Auto-restart requires manual login
  instagramMessenger.on('restart-requires-login', (data) => {
    logger.warn(`🔄 Auto-restart completed but requires manual login: ${data.reason}`);
    io.emit('messenger-restart-requires-login', {
      reason: data.reason,
      timestamp: data.timestamp,
      message: 'Messenger đã được khởi động lại nhưng cần đăng nhập thủ công để tiếp tục hàng chờ'
    });
  });

  // Thêm xử lý sự kiện messenger-stop-completed
  instagramMessenger.on('messenger-stop-completed', (data) => {
    logger.info(`🛑 Messenger stop completed: ${data.success ? 'success' : 'failed'}`);
    io.emit('messenger-stop-completed', data);
  });

  // Thêm xử lý sự kiện messenger-restarting
  instagramMessenger.on('messenger-restarting', (data) => {
    logger.info(`🔄 Messenger restarting due to: ${data.reason}`);
    io.emit('messenger-restarting', data);
  });

  // Thêm xử lý sự kiện messenger-stopped
  instagramMessenger.on('messenger-stopped', (data) => {
    logger.info(`⏹️ Messenger stopped due to: ${data.reason}`);
    io.emit('messenger-stopped', data);
  });

  // Thêm xử lý sự kiện messenger-restarted
  instagramMessenger.on('messenger-restarted', (data) => {
    logger.info(`✅ Messenger restarted successfully due to: ${data.reason}`);
    io.emit('messenger-restarted', data);
  });

  // Thêm xử lý sự kiện messenger-restart-failed
  instagramMessenger.on('messenger-restart-failed', (data) => {
    logger.error(`❌ Messenger restart failed due to: ${data.reason}, error: ${data.error}`);
    io.emit('messenger-restart-failed', data);
  });
}

// Socket.IO connection handling
io.on('connection', (socket) => {
  systemState.connectedClients++;
  logger.info(`🔌 Client connected: ${socket.id}. Total clients: ${systemState.connectedClients}`);
  logger.info(`🔌 Client info: ${socket.handshake.address} - ${socket.handshake.headers['user-agent']?.substring(0, 50)}...`);

  // Warning for multiple connections
  if (systemState.connectedClients > 2) {
    logger.warn(`⚠️ WARNING: ${systemState.connectedClients} clients connected! This may cause duplicate processing. Please close extra browser tabs.`);
  }

  // Send current system state to new client
  socket.emit('system-state', systemState);

  // Test connection handler
  socket.on('test-connection', (data) => {
    console.log('=== TEST CONNECTION RECEIVED ===', data);
    socket.emit('test-response', { message: 'Hello from server', timestamp: new Date().toISOString() });
  });

  // Handle client disconnect
  socket.on('disconnect', () => {
    systemState.connectedClients--;
    logger.info(`🔌 Client disconnected: ${socket.id}. Total clients: ${systemState.connectedClients}`);
  });

  // Handle manual message sending
  socket.on('send-message', async (data) => {
    try {
      await messageQueue.addMessage(data);
      socket.emit('message-queued', { success: true, data });
    } catch (error) {
      logger.error('Failed to queue message:', error);
      socket.emit('message-queued', { success: false, error: error.message });
    }
  });

  // Handle print request - now adds to messaging queue
  socket.on('print-comment', async (commentData, templateType = 'print_notification') => {
    try {
      logger.info(`🖨️ SOCKET Print/Message requested for comment: ${commentData.id} with template: ${templateType} from client: ${socket.id}`);
      logger.info(`📊 Current connected clients: ${systemState.connectedClients}`);

      // DUPLICATE PREVENTION: Check if this comment is already being processed
      const processingKey = `${commentData.id}_${templateType}`;
      logger.info(`🔍 Checking duplicate for key: ${processingKey}`);

      if (global.processingPrints && global.processingPrints.has(processingKey)) {
        logger.warn(`⚠️ DUPLICATE PREVENTED: Comment ${commentData.id} with template ${templateType} is already being processed`);
        logger.warn(`⚠️ Current processing set: ${Array.from(global.processingPrints).join(', ')}`);
        socket.emit('print-error', {
          commentId: commentData.id,
          error: 'This comment is already being processed'
        });
        return;
      }

      // Mark as processing
      if (!global.processingPrints) {
        global.processingPrints = new Set();
      }
      global.processingPrints.add(processingKey);
      logger.info(`✅ Added to processing set: ${processingKey}. Set size: ${global.processingPrints.size}`);

      // Add backup flag to comment data if it's a backup template
      const isBackup = templateType === 'backup_notification';
      const commentForPrint = {
        ...commentData,
        isBackup: isBackup
      };

      // Use printer service to print
      const printResult = await printerService.printComment(commentForPrint, 'comment');

      // ALWAYS add comment to message queue (regardless of messenger status)
      let messageQueued = false;
      let messageId = null;
      let customerType = 'regular';
      let queueReason = 'Auto messaging disabled';

      // Get auto messaging settings (to check if enabled later for logs)
      const autoMessageSettings = await database.getAutoMessageSettings();

      try {
        // Determine customer type
        const isRegularCustomer = await database.isRegularCustomer(commentData.username);
        customerType = isRegularCustomer ? 'vip' : 'regular';

        // Map templateType to actual template_type
        let actualTemplateType = 'normal';
        if (templateType === 'backup_notification') {
          actualTemplateType = 'backup';
        } else if (templateType === 'print_notification') {
          actualTemplateType = 'normal';
        }

        // Create message data for queue with specified template type
        const messageData = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          comment_id: commentData.id,
          username: commentData.username,
          original_comment: commentData.text,
          customer_type: customerType,
          template_type: actualTemplateType, // Use actual template type directly
          status: 'pending',
          // Removed priority - all messages processed in FIFO order
          retries: 0,
          max_retries: 3
        };

        // Add to database queue
        await database.addToMessageQueue(messageData);
        messageQueued = true;
        messageId = messageData.id;

        // Set appropriate reason message
        if (autoMessageSettings.enabled) {
          queueReason = instagramMessenger && instagramMessenger.getStatus().isRunning
            ? 'Added to queue - messenger running'
            : 'Added to queue - will process when messenger starts';
        } else {
          queueReason = 'Added to queue - auto messaging disabled';
        }

        // Update queued messages counter
        const oldQueued = systemState.queuedMessages;
        systemState.queuedMessages++;
        logger.info(`📊 QUEUE COUNTER UPDATE: queuedMessages ${oldQueued} → ${systemState.queuedMessages} (client: ${socket.id})`);
        io.emit('system-state', systemState);

        logger.info(`Comment printed and added to message queue: ${commentData.username} (${customerType}) with template: ${templateType} - ${queueReason}`);
      } catch (queueError) {
        logger.error('Failed to add message to queue:', queueError);
        queueReason = `Queue error: ${queueError.message}`;
      }

      // Update printed comments counter only after successful print
      if (printResult && printResult.success) {
        const oldCount = systemState.printedComments;
        systemState.printedComments++;
        logger.info(`📊 COUNTER UPDATE: printedComments ${oldCount} → ${systemState.printedComments} (client: ${socket.id})`);
        io.emit('system-state', systemState);
      }

      socket.emit('print-success', {
        commentId: commentData.id,
        comment: commentData,
        printResult: printResult,
        messageQueued: messageQueued,
        messageId: messageId,
        templateUsed: templateType,
        customerType: customerType,
        reason: queueReason
      });

    } catch (error) {
      logger.error('Print/Message error:', error);
      socket.emit('print-error', { commentId: commentData.id, error: error.message });
    } finally {
      // CLEANUP: Remove from processing set
      const processingKey = `${commentData.id}_${templateType}`;
      if (global.processingPrints) {
        global.processingPrints.delete(processingKey);
        logger.info(`🧹 Cleanup: Removed ${processingKey} from processing set`);
      }
    }
  });



  // Handle regular customer events from clients
  socket.on('regular-customer-added', (data) => {
    // Broadcast to all other clients (not the sender)
    socket.broadcast.emit('regular-customer-added', data);
    logger.info(`Regular customer added via socket: ${data.username}`);
  });

  socket.on('regular-customer-removed', (data) => {
    // Broadcast to all other clients (not the sender)
    socket.broadcast.emit('regular-customer-removed', data);
    logger.info(`Regular customer removed via socket: ${data.username}`);
  });

  // Printer service handlers
  socket.on('get-printer-settings', () => {
    try {
      const settings = printerService.getSettings();
      socket.emit('printer-settings', { success: true, settings });
    } catch (error) {
      logger.error('Failed to get printer settings:', error);
      socket.emit('printer-settings', { success: false, error: error.message });
    }
  });

  socket.on('update-printer-settings', async (newSettings) => {
    try {
      await printerService.updatePrinterSettings(newSettings.printer);
      await printerService.updatePrintFormats(newSettings.formats);
      await printerService.updateTemplates(newSettings.templates);

      socket.emit('printer-settings-updated', { success: true });
      logger.info('Printer settings updated successfully');
    } catch (error) {
      logger.error('Failed to update printer settings:', error);
      socket.emit('printer-settings-updated', { success: false, error: error.message });
    }
  });

  socket.on('detect-printers', async () => {
    try {
      const printers = await printerService.detectPrinters();
      socket.emit('printers-detected', { success: true, printers });
    } catch (error) {
      logger.error('Failed to detect printers:', error);
      socket.emit('printers-detected', { success: false, error: error.message });
    }
  });

  socket.on('test-print', async () => {
    try {
      const result = await printerService.testPrint();
      socket.emit('print-test-result', { success: true, result });
      logger.info('Test print completed successfully');
    } catch (error) {
      logger.error('Test print failed:', error);
      socket.emit('print-test-result', { success: false, error: error.message });
    }
  });

  socket.on('test-printer-connection', async (connectionSettings) => {
    try {
      let result;

      if (connectionSettings.connectionType === 'network') {
        // Update settings temporarily for test
        const oldSettings = printerService.printerSettings.networkSettings;
        printerService.printerSettings.networkSettings = connectionSettings.networkSettings;

        result = await printerService.testNetworkConnection();

        // Restore old settings
        printerService.printerSettings.networkSettings = oldSettings;
      } else if (connectionSettings.connectionType === 'usb') {
        // Update settings temporarily for test
        const oldSettings = printerService.printerSettings.usbSettings;
        printerService.printerSettings.usbSettings = connectionSettings.usbSettings;

        result = await printerService.testUSBConnection();

        // Restore old settings
        printerService.printerSettings.usbSettings = oldSettings;
      } else {
        result = { success: true, message: 'System printer connection test not implemented' };
      }

      socket.emit('connection-test-result', { success: true, ...result });
      logger.info('Connection test completed successfully');
    } catch (error) {
      logger.error('Connection test failed:', error);
      socket.emit('connection-test-result', { success: false, error: error.message });
    }
  });



  socket.on('template-updated', (data) => {
    logger.info('Template updated, broadcasting to all clients:', data);
    // Broadcast to all connected clients except sender
    socket.broadcast.emit('template-updated', data);
  });
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    systemState
  });
});

app.get('/api/stats', async (req, res) => {
  try {
    const stats = await database.getStats();

    // Get current queue stats for accurate counts
    let queueStats = { waiting: 0, sent: 0, failed: 0 };
    try {
      const queueData = await database.getMessageQueue();
      queueStats = {
        waiting: queueData.filter(m => m.status === 'pending').length,
        sent: queueData.filter(m => m.status === 'sent').length,
        failed: queueData.filter(m => m.status === 'failed').length
      };
    } catch (queueError) {
      logger.warn('Failed to get queue stats:', queueError);
    }

    const response = {
      ...systemState,
      // Add historical stats (but don't override session-based counters)
      printedCount: stats.printedCount, // Total historical printed records
      regularCustomers: stats.regularCustomers,
      sendOnceRecords: stats.sendOnceRecords,
      totalMessages: stats.totalMessages,
      // Historical queue stats for reference
      historicalSentMessages: queueStats.sent, // Total historical sent messages
      failedMessages: queueStats.failed,
      // Session-based counters (preserve from systemState)
      totalComments: sessionComments.length,
      printedComments: systemState.printedComments, // Session-based, not historical
      // Queue-based counters (real-time from queue)
      queuedMessages: queueStats.waiting,
      sentMessages: systemState.sentMessages, // Session-based, not historical
      sessionComments: sessionComments.length
    };

    logger.info('Stats API response:', {
      totalComments: response.totalComments,
      printedComments: response.printedComments,
      sentMessages: response.sentMessages,
      queuedMessages: response.queuedMessages
    });

    res.json(response);
  } catch (error) {
    logger.error('Failed to get stats:', error);
    res.status(500).json({ error: 'Failed to get stats' });
  }
});

// REMOVED: /api/comments endpoint - comments table no longer exists

// Session comments API (in-memory cache)
app.get('/api/session-comments', (req, res) => {
  try {
    // Lấy bình luận từ bộ nhớ global và sắp xếp theo thời gian
    const sortedComments = [...global.sessionComments].sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB;
    });

    logger.info(`📊 Session comments API called - returning ${sortedComments.length} comments`);
    res.json({
      success: true,
      comments: sortedComments,
      total: sortedComments.length,
      isScrapingActive: isScrapingActive,
      // FIXED: Removed undefined COMMENT_CONFIG that was causing 500 error
      unlimited: true // Indicate unlimited comments support
    });
  } catch (error) {
    logger.error('Failed to get session comments:', error);
    res.status(500).json({ error: 'Failed to get session comments' });
  }
});

// Removed session comments cleanup endpoints - no cleanup needed

// REMOVED: Session status and cleanup endpoints - no longer needed

// Message queue endpoints
app.get('/api/message-queue', async (req, res) => {
  try {
    const status = req.query.status || 'pending';
    const limit = parseInt(req.query.limit) || 100;
    logger.info(`Fetching message queue: status=${status}, limit=${limit}`);

    const messages = await database.getMessageQueue(status, limit);
    const queueStats = await database.getMessageQueueStats();

    logger.info(`Found ${messages.length} messages in queue, stats:`, queueStats);

    // Bổ sung instagram_message_id và thread_id cho các message đã hoàn thành
    for (const msg of messages) {
      if (msg.status === 'completed') {
        const sent = await database.getSentMessage({ queue_message_id: msg.id });
        msg.instagram_message_id = sent ? sent.instagram_message_id : null;
        msg.thread_id = sent ? sent.thread_id : null;
      }
    }

    res.json({
      success: true,
      messages,
      stats: queueStats
    });
  } catch (error) {
    logger.error('Error fetching message queue:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Thêm endpoint mới cho InstagrapiService để lấy tin nhắn tiếp theo
app.get('/api/message-queue/next', async (req, res) => {
  try {
    // Lấy tin nhắn đang chờ xử lý theo thứ tự FIFO
    const pendingMessages = await database.getMessageQueue('pending', 1);

    if (pendingMessages.length === 0) {
      // Không có tin nhắn đang chờ
      logger.info('InstagrapiService requested next message, but queue is empty');
      return res.json({
        success: true,
        message: null
      });
    }

    const message = pendingMessages[0];

    // Cập nhật trạng thái tin nhắn thành đang xử lý
    await database.updateMessageStatus(message.id, 'processing');

    // Lấy nội dung mẫu tin nhắn từ database nếu cần
    let messageContent = message.message;
    if (!messageContent) {
      try {
        // Lấy template dựa vào template_name và customer_type và template_type
        const templates = await database.getMessageTemplates(message.customer_type, message.template_type || 'normal');

        // Lọc các template đang hoạt động
        const activeTemplates = templates.filter(t => t.is_active);

        if (activeTemplates.length > 0) {
          // Sử dụng template đầu tiên tìm được
          let content = activeTemplates[0].template;
          // Thay thế các biến trong template
          content = content.replace(/{username}/g, message.username);
          content = content.replace(/{comment}/g, message.original_comment || '');
          messageContent = content;

          logger.info(`✅ Đã chọn template: ${activeTemplates[0].name} (${activeTemplates[0].template_type}) cho @${message.username}`);
        } else {
          // Nếu không tìm thấy template, sử dụng nội dung mặc định
          messageContent = `Cảm ơn bạn đã comment: "${message.original_comment}"`;
          logger.warn(`⚠️ Không tìm thấy template phù hợp cho customer_type=${message.customer_type}, template_type=${message.template_type || 'normal'}`);
        }
      } catch (templateError) {
        logger.error('Error getting message template:', templateError);
        messageContent = `Cảm ơn bạn đã comment.`;
      }
    } else {
      // Thay thế các biến trong nội dung tin nhắn đã có sẵn
      messageContent = messageContent.replace(/{{content}}/g, message.original_comment || '');
      messageContent = messageContent.replace(/{content}/g, message.original_comment || '');
      messageContent = messageContent.replace(/{username}/g, message.username);
      messageContent = messageContent.replace(/{comment}/g, message.original_comment || '');
    }

    // Cập nhật nội dung tin nhắn vào message
    message.message = messageContent;

    // Thêm threadId nếu có
    try {
      const threadInfo = await database.getInstagramThread(message.username);
      if (threadInfo) {
        message.threadId = threadInfo.thread_id;
      }
    } catch (threadError) {
      logger.warn(`Failed to get thread ID for @${message.username}:`, threadError);
    }

    logger.info(`✅ InstagrapiService requested next message: @${message.username}`);

    res.json({
      success: true,
      message: message
    });
  } catch (error) {
    logger.error('Error getting next message for InstagrapiService:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Thêm endpoint để cập nhật trạng thái tin nhắn sau khi xử lý
app.post('/api/message-queue/update-status', async (req, res) => {
  try {
    const { id, status, messageId, threadId, error, processed_at, messageContent } = req.body;
    logger.info(`📝 Updating message status: ${id} -> ${status}`);

    // Lưu dữ liệu bổ sung chỉ cho message_queue
    const additionalData = {};
    if (error) additionalData.error = error;
    if (processed_at) additionalData.processed_at = processed_at;

    // Cập nhật trạng thái tin nhắn (KHÔNG cập nhật threadId, messageId vào message_queue)
    await database.updateMessageStatus(id, status, additionalData);

    // Nếu là completed, lưu vào sent_messages
    if (status === 'completed' && messageId) {
      try {
        // Lấy thông tin tin nhắn
        const message = await database.getMessageById(id);
        if (message && message.username) {
          await database.addSentMessage({
            queue_message_id: id,
            instagram_message_id: messageId,
            thread_id: threadId,
            username: message.username,
            message_content: messageContent || message.original_comment
          });
          logger.info(`✅ Added message to sent_messages table: ${messageId} for @${message.username}`);
        }
      } catch (sentError) {
        logger.error(`❌ Failed to add to sent_messages table: ${sentError.message}`);
      }
    }

    // Cập nhật số liệu nếu thành công, xóa khỏi hàng đợi nếu thất bại quá số lần thử lại
    if (status === 'completed') {
      systemState.sentMessages++;
      logger.info(`✅ Message ${id} marked as completed`);

      // Lấy username chính xác để gửi event
      let username = null;
      try {
        const msg = await database.getMessageById(id);
        if (msg && msg.username) username = msg.username;
        else {
          // Nếu không có, thử lấy từ sent_messages
          const sent = await database.getSentMessage({ queue_message_id: id });
          if (sent && sent.username) username = sent.username;
        }
      } catch { }

      // Thông báo cho các clients
      io.emit('message-sent', {
        messageId: id,
        instaMessageId: messageId,
        threadId: threadId,
        status: 'completed',
        username: username || 'unknown'
      });

      // Emit updated system state
      io.emit('system-state', systemState);
    } else if (status === 'failed') {
      // Lấy thông tin tin nhắn
      const message = await database.getMessageById(id);

      // Nếu vượt quá số lần thử lại, xóa khỏi hàng đợi và ghi nhận thất bại
      if (message && message.retries >= message.max_retries) {
        await database.removeFromMessageQueue(id);
        // Ghi nhận vào bảng tin nhắn thất bại
        await database.addFailedMessage({
          message_id: id,
          username: message.username,
          original_comment: message.original_comment,
          customer_type: message.customer_type,
          template_name: message.template_name,
          template_type: message.template_type,
          error: error || 'Unknown error',
          created_at: new Date().toISOString(),
          status: 'failed'
        });
        logger.warn(`❌ Message ${id} failed and removed from queue (max retries reached)`);

        systemState.errors++;
        systemState.queuedMessages = Math.max(0, systemState.queuedMessages - 1);

        // Thông báo cho các clients
        io.emit('message-failed', {
          messageId: id,
          error: error || 'Unknown error',
          status: 'failed',
          username: message.username || 'unknown'
        });

        // Emit updated system state
        io.emit('system-state', systemState);
      } else {
        // Tăng số lần thử lại
        await database.incrementRetryCount(id);
        logger.info(`🔄 Message ${id} marked for retry`);
      }
    }

    // Thông báo realtime cho client về trạng thái mới
    io.emit('message-status-update', { id, status });
    io.emit('message-queue-updated');
    io.emit('message-status-update', { id, status }); // Đảm bảo phát nhiều nơi đều nhận được

    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    logger.error('Error updating message status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.delete('/api/message-queue/:id', async (req, res) => {
  try {
    const messageId = req.params.id;
    await database.removeFromMessageQueue(messageId);

    res.json({
      success: true,
      message: `Message ${messageId} removed from queue`
    });

    // Emit update to all connected clients
    io.emit('message-queue-updated');

  } catch (error) {
    logger.error(`Error removing message ${req.params.id} from queue:`, error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.delete('/api/message-queue', async (req, res) => {
  try {
    // Get all messages to count them
    const allMessages = await database.getMessageQueue('all', 10000);
    const count = allMessages.length;

    // Xóa hẳn tất cả tin nhắn khỏi database
    for (const message of allMessages) {
      await database.removeFromMessageQueue(message.id);
    }

    res.json({
      success: true,
      message: `${count} messages đã xóa khỏi hàng chờ`
    });

    // Emit update to all connected clients
    io.emit('message-queue-updated');

  } catch (error) {
    logger.error('Error clearing message queue:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.get('/api/message-queue/stats', async (req, res) => {
  try {
    const stats = await database.getMessageQueueStats();

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    logger.error('Failed to get message queue stats:', error);
    res.status(500).json({ error: 'Failed to get message queue stats' });
  }
});

app.post('/api/message-queue/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 7 } = req.body;

    const deletedCount = await database.cleanupOldQueueMessages(olderThanDays);

    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} old queue messages`,
      deletedCount
    });
  } catch (error) {
    logger.error('Failed to cleanup message queue:', error);
    res.status(500).json({ error: 'Failed to cleanup message queue' });
  }
});

// Queue message endpoint (for resending messages from history)
app.post('/api/queue-message', async (req, res) => {
  try {
    const { username, comment_text, source = 'manual' } = req.body;

    if (!username || !comment_text) {
      return res.status(400).json({ error: 'Username and comment_text are required' });
    }

    logger.info(`📤 Manual message queue requested for user: ${username} (source: ${source})`);

    // Get customer type for template selection
    const customerType = await database.getCustomerType(username);

    // Create message data for queue
    const messageData = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      comment_id: `resend_${Date.now()}`,
      username: username,
      original_comment: comment_text,
      customer_type: customerType,
      template_type: 'normal', // Use normal template for resends
      status: 'pending',
      retries: 0,
      max_retries: 3
    };

    // Add to database queue
    await database.addToMessageQueue(messageData);

    // Phát event trạng thái chờ
    io.emit('message-status-update', { id: messageData.id, status: 'pending' });

    // Update queued messages counter
    systemState.queuedMessages++;
    io.emit('system-state', systemState);

    // Emit queue update event
    io.emit('message-queued', {
      username: username,
      messageId: messageData.id,
      source: source
    });

    logger.info(`Message queued successfully for ${username}: ${messageData.id}`);
    res.json({
      success: true,
      message: `Message queued for @${username}`,
      messageId: messageData.id,
      customerType: customerType
    });

  } catch (error) {
    logger.error('Failed to queue message:', error);
    res.status(500).json({ error: 'Failed to queue message: ' + error.message });
  }
});


// Send_once history endpoints
app.get('/api/send-once-history', async (req, res) => {
  try {
    const { username, limit = 100 } = req.query;

    const history = await database.getSendOnceHistory(username, parseInt(limit));

    res.json({
      success: true,
      history,
      total: history.length
    });
  } catch (error) {
    logger.error('Failed to get send_once history:', error);
    res.status(500).json({ error: 'Failed to get send_once history' });
  }
});

app.post('/api/send-once-history/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 2 } = req.body;

    const deletedCount = await database.cleanupOldSendOnceHistory(olderThanDays);

    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} send_once history records older than ${olderThanDays} days`,
      deletedCount,
      cleanupDays: olderThanDays
    });
  } catch (error) {
    logger.error('Failed to cleanup send_once history:', error);
    res.status(500).json({ error: 'Failed to cleanup send_once history' });
  }
});

// Printed history cleanup endpoint
app.post('/api/printed-history/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 90 } = req.body;

    const deletedCount = await database.cleanupOldPrintedHistory(olderThanDays);

    res.json({
      success: true,
      message: `Hard deleted ${deletedCount} old printed history records older than ${olderThanDays} days`,
      deletedCount,
      cleanupDays: olderThanDays
    });
  } catch (error) {
    logger.error('Failed to cleanup printed history:', error);
    res.status(500).json({ error: 'Failed to cleanup printed history' });
  }
});

// Test endpoint to add test comments (no cleanup)
app.post('/api/test-add-comments', (req, res) => {
  try {
    const { addComments = 60 } = req.body;

    // Add fake comments for testing
    for (let i = 0; i < addComments; i++) {
      sessionComments.push({
        id: `test_${Date.now()}_${i}`,
        username: `testuser${i}`,
        text: `Test comment ${i}`,
        timestamp: new Date().toISOString(),
        sessionId: Date.now()
      });
    }

    // Update system state
    systemState.totalComments = sessionComments.length;

    res.json({
      success: true,
      message: `Added ${addComments} test comments`,
      totalComments: sessionComments.length
    });
  } catch (error) {
    logger.error('Failed to add test comments:', error);
    res.status(500).json({ error: 'Failed to add test comments' });
  }
});

// REMOVED: Auto Messages API - not used, replaced by message_templates system

// Instagram Threads API for cached thread IDs
app.get('/api/instagram-threads', async (req, res) => {
  try {
    const threads = await database.getAllInstagramThreads();
    logger.info(`📊 Instagram threads in database: ${threads.length} threads`);
    threads.forEach(thread => {
      logger.info(`  - @${thread.username}: ${thread.thread_id} (last used: ${thread.last_used})`);
    });

    res.json({
      success: true,
      threads,
      total: threads.length
    });
  } catch (error) {
    logger.error('Failed to get Instagram threads:', error);
    res.status(500).json({ error: error.message });
  }
});

// Instagram Threads MongoDB sync endpoints
app.post('/api/instagram-threads/sync-to-mongo', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    const localThreads = await database.getInstagramThreadsForSync();
    const result = await mongoDBService.syncThreadsToMongo(localThreads);

    res.json({
      success: true,
      ...result,
      message: `Synced ${result.count} threads to MongoDB`
    });
  } catch (error) {
    logger.error('Failed to sync threads to MongoDB:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/instagram-threads/sync-from-mongo', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    // Get threads from MongoDB
    const mongoThreads = await mongoDBService.syncThreadsFromMongo();

    // Perform smart bidirectional sync
    const syncResult = await database.smartSyncThreadsWithMongoDB(mongoThreads);

    // Update last sync time
    await database.saveSetting('mongodb_threads_last_sync', new Date().toISOString());

    res.json({
      success: true,
      ...syncResult,
      message: `Smart sync completed: +${syncResult.addedToLocal} local, +${syncResult.addedToMongo} MongoDB, ~${syncResult.updatedLocal + syncResult.updatedMongo} updated`
    });
  } catch (error) {
    logger.error('Failed to sync threads from MongoDB:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/instagram-threads/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const deletedCount = await database.deleteInstagramThread(username);

    if (deletedCount > 0) {
      res.json({
        success: true,
        message: `Deleted thread for @${username}`,
        deletedCount
      });
    } else {
      res.status(404).json({ error: 'Thread not found' });
    }
  } catch (error) {
    logger.error('Failed to delete Instagram thread:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/instagram-threads/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const deletedCount = await database.deleteInstagramThread(username);

    if (deletedCount > 0) {
      res.json({ success: true, message: `Deleted thread cache for @${username}` });
    } else {
      res.status(404).json({ error: `No cached thread found for @${username}` });
    }
  } catch (error) {
    logger.error('Failed to delete Instagram thread:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/instagram-threads/cleanup', async (req, res) => {
  try {
    const { olderThanDays = 30 } = req.body;
    const deletedCount = await database.cleanupOldInstagramThreads(olderThanDays);

    res.json({
      success: true,
      message: `Cleaned up ${deletedCount} old thread caches`,
      deletedCount
    });
  } catch (error) {
    logger.error('Failed to cleanup Instagram threads:', error);
    res.status(500).json({ error: error.message });
  }
});

// Instagram Messenger API
app.post('/api/start-messenger', async (req, res) => {
  try {
    const { credentials, useSavedCookies } = req.body;

    let savedCookies = null;
    let finalCredentials = credentials;

    // Load saved cookies if requested (InstagramMessenger will handle this internally now)
    if (useSavedCookies) {
      logger.info('Requested to use saved cookies - InstagramMessenger will load from database');
    } else {
      logger.info('Using provided credentials for fresh login');
    }

    // If using saved cookies, don't require credentials
    if (!useSavedCookies && (!credentials || !credentials.username || !credentials.password)) {
      return res.status(400).json({ error: 'Messenger credentials are required when not using saved cookies' });
    }

    // Start the messenger (it will auto-load cookies if useSavedCookies=true)
    const result = await instagramMessenger.start(
      useSavedCookies ? null : finalCredentials, // Pass null if using saved cookies
      null // Let InstagramMessenger load cookies from database
    );

    if (result && result.pending2FA) {
      logger.info('Instagram Messenger requires 2FA');
      res.json({
        success: false,
        pending2FA: true,
        message: 'Two-factor authentication required. Please complete 2FA in the browser window.'
      });
    } else {
      logger.info('Instagram Messenger started successfully');

      res.json({
        success: true,
        message: useSavedCookies ? 'Messenger started with saved cookies' : 'Messenger started with credentials',
        usingSavedCookies: useSavedCookies
      });
    }
  } catch (error) {
    logger.error('Failed to start messenger:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/stop-messenger', async (req, res) => {
  try {
    await instagramMessenger.stop();

    logger.info('Instagram Messenger stopped');
    res.json({ success: true, message: 'Messenger stopped' });
  } catch (error) {
    logger.error('Failed to stop messenger:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/logout-messenger', async (req, res) => {
  try {
    logger.info('=== LOGOUT MESSENGER REQUEST ===');

    // Check cookies before clearing
    const cookiesBefore = await database.getMessengerSavedCookies();
    logger.info('Cookies before clear:', cookiesBefore ? `User: ${cookiesBefore.username}` : 'No cookies');

    // Clear messenger cookies using the correct method
    await database.clearMessengerSavedCookies();
    logger.info('clearMessengerSavedCookies() completed');

    // Verify cookies are actually cleared
    const cookiesAfter = await database.getMessengerSavedCookies();
    logger.info('Cookies after clear:', cookiesAfter ? `User: ${cookiesAfter.username}` : 'No cookies');

    // Logout from browser and stop messenger if running
    if (instagramMessenger && instagramMessenger.isRunning) {
      logger.info('Logging out and stopping running messenger...');

      try {
        // First logout to clear browser cookies
        const browserLogoutSuccess = await instagramMessenger.logout();
        logger.info(`Browser logout completed: ${browserLogoutSuccess}`);

        // Then stop the messenger
        await instagramMessenger.stop();
        logger.info('Messenger stopped');
      } catch (logoutError) {
        logger.error('Error during logout process:', logoutError);
        // Still try to stop messenger even if logout fails
        await instagramMessenger.stop();
      }
    }

    if (cookiesAfter) {
      logger.error('WARNING: Cookies still exist after clearing!');
      res.status(500).json({
        success: false,
        error: 'Failed to clear cookies completely',
        cookiesStillExist: true
      });
    } else {
      logger.info('Messenger cookies cleared and logged out successfully');
      res.json({ success: true, message: 'Logged out from messenger' });
    }
  } catch (error) {
    logger.error('Failed to logout messenger:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/messenger-status', (req, res) => {
  try {
    const status = {
      isRunning: instagramMessenger.isRunning,
      isLoggedIn: instagramMessenger.isLoggedIn,
      queueLength: instagramMessenger.messageQueue ? instagramMessenger.messageQueue.length : 0,
      isProcessingQueue: instagramMessenger.isProcessingQueue,
      browserConnected: instagramMessenger.browser !== null,
      pageConnected: instagramMessenger.page !== null,
      currentUsername: instagramMessenger.currentUsername || null
    };

    res.json({ success: true, status, isRunning: status.isRunning });
  } catch (error) {
    logger.error('Failed to get messenger status:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/scraper-status', (req, res) => {
  try {
    // Get status from both systemState and scraper instance
    const systemStatus = {
      isRunning: systemState.isRunning,
      isScrapingActive: isScrapingActive,
      startTime: systemState.startTime,
      totalComments: systemState.totalComments,
      processedComments: systemState.processedComments,
      sessionComments: sessionComments.length,
      scraperConnected: !!instagramScraper
    };

    let scraperInstanceStatus = null;
    if (instagramScraper) {
      try {
        scraperInstanceStatus = instagramScraper.getStatus();
      } catch (error) {
        scraperInstanceStatus = { error: error.message };
      }
    }

    const combinedStatus = {
      system: systemStatus,
      scraper: scraperInstanceStatus,
      isRunning: systemStatus.isRunning || (scraperInstanceStatus && scraperInstanceStatus.isRunning)
    };

    res.json({ success: true, status: combinedStatus, isRunning: combinedStatus.isRunning });
  } catch (error) {
    logger.error('Failed to get scraper status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Message Templates API
app.get('/api/message-templates', async (req, res) => {
  try {
    const { customer_type, active_only } = req.query;

    let templates;
    if (active_only === 'true') {
      templates = await database.getActiveMessageTemplates(customer_type);
    } else {
      templates = await database.getMessageTemplates(customer_type);
    }

    res.json({ success: true, templates });
  } catch (error) {
    logger.error('Failed to get message templates:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/message-templates', async (req, res) => {
  try {
    const { name, template, description, variables, is_active, customer_type, send_once, template_type } = req.body;

    if (!name || !template) {
      return res.status(400).json({ error: 'Name and template are required' });
    }

    await database.saveMessageTemplate({
      name,
      template,
      description: description || '',
      variables: variables || '[]',
      customer_type: customer_type || 'regular',
      template_type: template_type || 'normal',
      is_active: is_active === true,
      send_once: send_once === true
    });

    res.json({ success: true, message: 'Template created successfully' });
  } catch (error) {
    logger.error('Failed to create message template:', error);

    // Handle specific SQLite constraint errors
    if (error.message.includes('UNIQUE constraint')) {
      res.status(400).json({
        error: `Template với tên "${req.body.name}" đã tồn tại cho loại khách hàng này`,
        code: 'DUPLICATE_NAME'
      });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

app.put('/api/message-templates/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, template, description, variables, is_active, customer_type, send_once, template_type } = req.body;

    logger.info(`Updating template ${id}: is_active = ${is_active} (type: ${typeof is_active})`);

    if (!name || !template) {
      return res.status(400).json({ error: 'Name and template are required' });
    }

    await database.saveMessageTemplate({
      id,
      name,
      template,
      description: description || '',
      variables: variables || '[]',
      customer_type: customer_type || 'regular',
      template_type: template_type || 'normal',
      is_active: Boolean(is_active),
      send_once: Boolean(send_once)
    });

    res.json({ success: true });
  } catch (error) {
    logger.error('Failed to update message template:', error);

    // Handle specific SQLite constraint errors
    if (error.message.includes('UNIQUE constraint')) {
      res.status(400).json({
        error: `Template với tên "${req.body.name}" đã tồn tại cho loại khách hàng này`,
        code: 'DUPLICATE_NAME'
      });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

app.delete('/api/message-templates/:id', async (req, res) => {
  try {
    const { id } = req.params;

    await database.deleteMessageTemplate(id);

    res.json({ success: true });
  } catch (error) {
    logger.error('Failed to delete message template:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin endpoints for default templates management
app.post('/api/message-templates/recreate-defaults', async (req, res) => {
  try {
    const result = await database.recreateDefaultTemplates();
    res.json(result);
  } catch (error) {
    logger.error('Failed to recreate default templates:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/message-templates/reset-flag', async (req, res) => {
  try {
    const result = await database.resetDefaultTemplatesFlag();
    res.json(result);
  } catch (error) {
    logger.error('Failed to reset default templates flag:', error);
    res.status(500).json({ error: error.message });
  }
});

// Clean duplicate templates API
app.post('/api/clean-duplicate-templates', async (req, res) => {
  try {
    const { name, customer_type } = req.body;

    if (!name || !customer_type) {
      return res.status(400).json({ error: 'Name and customer_type are required' });
    }

    // Find and delete existing templates with same name and customer_type
    const existingTemplates = await database.getMessageTemplates(customer_type);
    const duplicates = existingTemplates.filter(t => t.name === name);

    for (const duplicate of duplicates) {
      await database.deleteMessageTemplate(duplicate.id);
      logger.info(`Deleted duplicate template: ${duplicate.name} (${duplicate.customer_type})`);
    }

    res.json({
      success: true,
      message: `Deleted ${duplicates.length} duplicate template(s)`,
      deletedCount: duplicates.length
    });
  } catch (error) {
    logger.error('Failed to clean duplicate templates:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/messenger-status', (req, res) => {
  try {
    const status = instagramMessenger.getStatus();
    res.json({ success: true, status });
  } catch (error) {
    logger.error('Failed to get messenger status:', error);
    res.status(500).json({ error: error.message });
  }
});

// REMOVED: Duplicate endpoint - using the one at line 925 instead

// API endpoints cho Instagrapi messenger
app.post('/api/messenger/set-mode', (req, res) => {
  try {
    const { useInstagrapi } = req.body;
    const result = messageQueue.setUseInstagrapi(useInstagrapi);
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    logger.error('Failed to set messenger mode:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to set messenger mode'
    });
  }
});

app.post('/api/messenger/instagrapi/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Validate inputs
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    const result = await instagrapiMessenger.login(username, password);

    res.json({
      success: result.status === 'success',
      message: result.message || 'Login attempt processed',
      result
    });
  } catch (error) {
    logger.error('Failed to login with instagrapi:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to login with instagrapi'
    });
  }
});

app.post('/api/messenger/instagrapi/login-by-session', async (req, res) => {
  try {
    const { sessionId } = req.body;

    // Validate inputs
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    const result = await instagrapiMessenger.loginBySessionId(sessionId);

    res.json({
      success: result.status === 'success',
      message: result.message || 'Login by session attempt processed',
      result
    });
  } catch (error) {
    logger.error('Failed to login with session ID:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to login with session ID'
    });
  }
});

app.post('/api/messenger/instagrapi/start', async (req, res) => {
  try {
    const result = await instagrapiMessenger.startQueueProcessor();

    res.json({
      success: result.status === 'success',
      message: result.message || 'Started instagrapi queue processor',
      result
    });
  } catch (error) {
    logger.error('Failed to start instagrapi queue processor:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to start instagrapi queue processor'
    });
  }
});

app.post('/api/messenger/instagrapi/stop', async (req, res) => {
  try {
    const result = await instagrapiMessenger.stopQueueProcessor();

    res.json({
      success: result.status === 'success',
      message: result.message || 'Stopped instagrapi queue processor',
      result
    });
  } catch (error) {
    logger.error('Failed to stop instagrapi queue processor:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to stop instagrapi queue processor'
    });
  }
});

app.get('/api/messenger/instagrapi/status', async (req, res) => {
  try {
    const status = instagrapiMessenger.getStatus();

    res.json({
      success: true,
      status
    });
  } catch (error) {
    logger.error('Failed to get instagrapi status:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to get instagrapi status'
    });
  }
});

app.post('/api/start-scraping', async (req, res) => {
  try {
    const { liveUsername, credentials, useSavedCookies } = req.body;

    if (!liveUsername) {
      return res.status(400).json({ error: 'Live username is required' });
    }

    // Reset session comments and counters when starting new scraping session
    sessionComments = [];
    isScrapingActive = true;

    // Reset session counters for new scraping session
    systemState.totalComments = 0;
    systemState.printedComments = 0;

    logger.info('Session comments and counters reset for new scraping session');

    let savedCookies = null;
    let finalCredentials = credentials;

    // Try to use saved cookies first
    if (useSavedCookies) {
      try {
        const cookieData = await database.getSavedCookies(null, 'scraper');
        if (cookieData) {
          savedCookies = cookieData.cookies;
          logger.info(`Using saved scraper cookies for user: ${cookieData.username}`);
        } else {
          logger.warn('No saved scraper cookies found, will use credentials');
        }
      } catch (error) {
        logger.error('Failed to load saved scraper cookies:', error);
      }
    }

    // If no saved cookies and no credentials provided, return error
    if (!savedCookies && (!credentials || !credentials.username || !credentials.password)) {
      return res.status(400).json({ error: 'Either saved cookies or credentials are required' });
    }

    // Create profile URL from username (will check for live stream from profile)
    const liveUrl = `https://www.instagram.com/${liveUsername}/`;

    await instagramScraper.start(liveUrl, finalCredentials, savedCookies);
    systemState.isRunning = true;
    systemState.startTime = new Date();

    // Emit updated system state with reset counters
    io.emit('system-state', systemState);

    logger.info(`Instagram API monitoring started for user: ${liveUsername}`);
    res.json({ success: true, message: 'Scraping started' });
  } catch (error) {
    logger.error('Failed to start scraping:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/stop-scraping', async (req, res) => {
  try {
    await instagramScraper.stop();
    systemState.isRunning = false;
    systemState.startTime = null;
    isScrapingActive = false;

    // Reset scraping-related counters when stopping
    systemState.totalComments = 0;
    systemState.printedComments = 0;

    // Emit updated system state
    io.emit('system-state', systemState);

    logger.info('Instagram API monitoring stopped and counters reset');
    res.json({ success: true, message: 'Scraping stopped' });
  } catch (error) {
    logger.error('Failed to stop scraping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get scraping mode and stats
app.get('/api/scraping-mode', (req, res) => {
  try {
    const scrapingMode = instagramScraper.getScrapingMode();
    res.json({
      success: true,
      mode: scrapingMode,
      isActive: isScrapingActive
    });
  } catch (error) {
    logger.error('Failed to get scraping mode:', error);
    res.status(500).json({ error: error.message });
  }
});

// Scraping mode info (API only now)
app.get('/api/scraping-info', (req, res) => {
  try {
    const scrapingMode = instagramScraper.getScrapingMode();
    res.json({
      success: true,
      message: 'Instagram API Interception Only - DOM scraping removed',
      mode: scrapingMode,
      isActive: isScrapingActive
    });
  } catch (error) {
    logger.error('Failed to get scraping info:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/debug-comments', async (req, res) => {
  try {
    if (!instagramScraper || !systemState.isRunning) {
      return res.status(400).json({ error: 'Scraper is not running' });
    }

    logger.info('Debug comment detection triggered from API');
    await instagramScraper.forceDebug();

    res.json({ success: true, message: 'Debug completed - check server logs' });
  } catch (error) {
    logger.error('Failed to debug comments:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/test-comment', async (req, res) => {
  try {
    logger.info('=== TESTING COMMENT EMISSION ===');

    // Create a test comment
    const testComment = {
      id: `test_comment_${Date.now()}`,
      username: 'test_user',
      text: 'This is a test comment to verify the system works',
      timestamp: new Date().toLocaleString('sv-SE', {
        timeZone: 'Asia/Ho_Chi_Minh'
      }),
      element_index: 0,
      element_class: 'test-class'
    };

    logger.info('Test comment created:', testComment);
    logger.info(`Emitting to ${systemState.connectedClients} connected clients`);

    // Emit directly to test the pipeline
    io.emit('new-comment', testComment);

    logger.info('=== TEST COMMENT EMITTED ===');
    res.json({ success: true, message: 'Test comment emitted', comment: testComment });
  } catch (error) {
    logger.error('Failed to emit test comment:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add debug comments API
app.post('/api/add-debug-comments', async (req, res) => {
  try {
    const { count = 1 } = req.body;

    // Check if scraper exists and is running
    if (!instagramScraper) {
      return res.status(400).json({ error: 'Scraper service not available' });
    }

    // Check scraper running status directly from scraper instance
    const scraperStatus = instagramScraper.getStatus();
    if (!scraperStatus.isRunning) {
      return res.status(400).json({
        error: 'Scraper must be running to add debug comments',
        scraperStatus: scraperStatus
      });
    }

    if (count < 1 || count > 100) {
      return res.status(400).json({ error: 'Count must be between 1 and 100' });
    }

    logger.info(`Adding ${count} debug comments via API (scraper running: ${scraperStatus.isRunning})`);
    const addedComments = await instagramScraper.addDebugComments(count);

    res.json({
      success: true,
      message: `Added ${count} debug comments successfully`,
      comments: addedComments,
      scraperStatus: scraperStatus
    });
  } catch (error) {
    logger.error('Failed to add debug comments:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/test-network-connection', async (req, res) => {
  try {
    const { connectionType, networkSettings, usbSettings } = req.body;

    logger.info('Testing printer connection:', { connectionType, networkSettings, usbSettings });

    if (connectionType === 'network') {
      if (!networkSettings || !networkSettings.ipAddress) {
        return res.status(400).json({ error: 'IP address is required for network connection' });
      }

      // Update settings temporarily for test
      const oldSettings = printerService.printerSettings.networkSettings;
      printerService.printerSettings.networkSettings = networkSettings;

      const result = await printerService.testNetworkConnection();

      // Restore old settings
      printerService.printerSettings.networkSettings = oldSettings;

      res.json({ success: true, ...result });
    } else if (connectionType === 'usb') {
      if (!usbSettings || !usbSettings.vendorId || !usbSettings.productId) {
        return res.status(400).json({ error: 'Vendor ID and Product ID are required for USB connection' });
      }

      // Update settings temporarily for test
      const oldSettings = printerService.printerSettings.usbSettings;
      printerService.printerSettings.usbSettings = usbSettings;

      const result = await printerService.testUSBConnection();

      // Restore old settings
      printerService.printerSettings.usbSettings = oldSettings;

      res.json({ success: true, ...result });
    } else {
      res.json({ success: true, message: 'System printer connection test not implemented' });
    }
  } catch (error) {
    logger.error('Failed to test printer connection:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get available printers API
app.get('/api/printers', async (req, res) => {
  try {
    // Get system printers using Windows PowerShell command
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    // Use PowerShell to get printer list
    const command = 'powershell "Get-Printer | Select-Object Name, PrinterStatus, DriverName | ConvertTo-Json"';

    try {
      const { stdout } = await execAsync(command);
      let printers = [];

      if (stdout.trim()) {
        const printerData = JSON.parse(stdout);
        // Handle both single printer (object) and multiple printers (array)
        const printersArray = Array.isArray(printerData) ? printerData : [printerData];

        printers = printersArray.map(p => ({
          name: p.Name || 'Unknown',
          status: p.PrinterStatus === 0 ? 'ready' : 'not_ready',
          isDefault: false,
          description: p.DriverName || '',
          location: ''
        }));
      }

      logger.info(`Found ${printers.length} system printers`);
      res.json({ success: true, printers });
    } catch (cmdError) {
      logger.warn('Failed to get printers via PowerShell:', cmdError.message);
      // Return mock printers for development
      const mockPrinters = [
        { name: 'Microsoft Print to PDF', status: 'ready', isDefault: false, description: 'PDF Printer', location: '' },
        { name: 'Microsoft XPS Document Writer', status: 'ready', isDefault: false, description: 'XPS Printer', location: '' }
      ];
      res.json({ success: true, printers: mockPrinters });
    }
  } catch (error) {
    logger.error('Failed to get printers:', error);
    // Return empty list instead of error to prevent UI crash
    res.json({ success: true, printers: [] });
  }
});



// Cookie management API
app.get('/api/saved-cookies', async (req, res) => {
  try {
    // Only get scraper cookies
    const cookieData = await database.getSavedCookies(null, 'scraper');
    res.json({
      success: true,
      hasCookies: !!cookieData,
      username: cookieData?.username || null
    });
  } catch (error) {
    logger.error('Failed to get saved scraper cookies:', error);
    res.status(500).json({ error: error.message });
  }
});

// Messenger Cookie management API
app.get('/api/messenger-saved-cookies', async (req, res) => {
  try {
    const cookieData = await database.getMessengerSavedCookies();
    logger.info('=== GET MESSENGER COOKIES ===');
    logger.info('Cookie data found:', cookieData ? `User: ${cookieData.username}` : 'No cookies');

    res.json({
      success: true,
      hasCookies: !!cookieData,
      username: cookieData?.username || null
    });
  } catch (error) {
    logger.error('Error getting messenger saved cookies:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/logout', async (req, res) => {
  try {
    // Only clear scraper cookies, not messenger cookies
    await database.clearCookies(null, 'scraper');
    logger.info('Scraper cookies cleared successfully');
    res.json({ success: true, message: 'Logged out successfully from scraper' });
  } catch (error) {
    logger.error('Failed to clear scraper cookies:', error);
    res.status(500).json({ error: error.message });
  }
});



// Regular customers API
app.get('/api/regular-customers', async (req, res) => {
  try {
    const customers = await database.getRegularCustomers();
    res.json({ success: true, customers });
  } catch (error) {
    logger.error('Failed to get regular customers:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/regular-customers', async (req, res) => {
  try {
    const { username, notes = '' } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    await database.addRegularCustomer(username, notes);

    // Broadcast to all connected clients
    io.emit('regular-customer-added', { username });

    res.json({ success: true, message: `Added regular customer: ${username}` });
  } catch (error) {
    logger.error('Failed to add regular customer:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/regular-customers/:username', async (req, res) => {
  try {
    const { username } = req.params;
    await database.removeRegularCustomer(username);

    // Broadcast to all connected clients
    io.emit('regular-customer-removed', { username });

    res.json({ success: true, message: `Removed regular customer: ${username}` });
  } catch (error) {
    logger.error('Failed to remove regular customer:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/regular-customers/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const isRegular = await database.isRegularCustomer(username);
    res.json({ success: true, isRegular });
  } catch (error) {
    logger.error('Failed to check regular customer:', error);
    res.status(500).json({ error: error.message });
  }
});

// Print history API
app.get('/api/printed-comments', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      search = '',
      dateFilter = 'all',
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays
    } = req.query;

    const result = await database.getPrintedHistory({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      dateFilter,
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays: applyTimeToAllDays === 'true'
    });

    res.json({ success: true, ...result });
  } catch (error) {
    logger.error('Failed to get printed comments:', error);
    res.status(500).json({ error: error.message });
  }
});

// History endpoint (alias for printed-comments)
app.get('/api/history', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      search = '',
      dateFilter = 'all',
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays
    } = req.query;

    const result = await database.getPrintedHistory({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      dateFilter,
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays: applyTimeToAllDays === 'true'
    });

    res.json({ success: true, ...result });
  } catch (error) {
    logger.error('Failed to get history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Deleted history endpoint
app.get('/api/history/deleted', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      search = '',
      dateFilter = 'all',
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays
    } = req.query;

    const result = await database.getDeletedPrintedHistory({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      dateFilter,
      startDate,
      endDate,
      startTime,
      endTime,
      filterType,
      applyTimeToAllDays: applyTimeToAllDays === 'true'
    });

    res.json({ success: true, ...result });
  } catch (error) {
    logger.error('Failed to get deleted history:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/print-user-history', async (req, res) => {
  try {
    const { username } = req.body;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    // Get all printed history from this user
    const historyResult = await database.getPrintedHistory({
      page: 1,
      limit: 1000, // Get all records for this user
      search: username
    });

    const userComments = historyResult.history || [];

    if (userComments.length === 0) {
      return res.status(404).json({ error: 'No printed history found for this user' });
    }

    // Convert printed_history format to expected comment format for printing
    const commentsForPrint = userComments.map(item => ({
      id: item.comment_id,
      username: item.username,
      text: item.comment_text,
      timestamp: item.printed_at
    }));

    // Print user history using history format
    const result = await printerService.printUserHistory(username, commentsForPrint);

    logger.info(`User history printed successfully: ${username} (${userComments.length} comments)`);
    res.json({ success: true, result, commentsCount: userComments.length });
  } catch (error) {
    logger.error('Failed to print user history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Print single comment API
app.post('/api/print-comment', async (req, res) => {
  try {
    const { username, text, timestamp, id } = req.body;

    if (!username || !text) {
      return res.status(400).json({ error: 'Username and text are required' });
    }

    logger.info(`🖨️ API Manual reprint requested for user: ${username}`);

    // Create comment data for printing
    const commentData = {
      id: id || `reprint_${Date.now()}`,
      username,
      text,
      timestamp: timestamp || new Date().toLocaleString('sv-SE', {
        timeZone: 'Asia/Ho_Chi_Minh'
      })
    };

    // Print the comment
    const printResult = await printerService.printComment(commentData, 'comment');

    if (printResult.success) {
      // NOTE: Do NOT increment printedComments counter here to avoid duplicate counting
      // The socket handler already handles counter increments for normal prints
      // This endpoint is only for manual reprints from history page

      // Emit print success event (for any listeners)
      io.emit('print-success', {
        commentId: commentData.id,
        comment: commentData,
        printResult: printResult,
        messageQueued: false,
        reason: 'Manual reprint'
      });

      logger.info(`Comment reprinted successfully: ${username}`);
      res.json({ success: true, result: printResult });
    } else {
      throw new Error(printResult.error || 'Print failed');
    }
  } catch (error) {
    logger.error('Failed to print comment:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete single comment from printed history
app.delete('/api/comments/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Comment ID is required' });
    }

    // Delete from printed_history table (this is what the history page shows)
    const deletedCount = await database.deletePrintedHistoryRecord(id);

    if (deletedCount > 0) {
      logger.info(`Printed history record deleted: ${id}`);

      // Emit real-time event for history page refresh
      io.emit('printed-history-updated', {
        action: 'delete',
        recordId: id,
        timestamp: new Date().toISOString()
      });

      res.json({ success: true, message: 'Comment deleted successfully' });
    } else {
      res.status(404).json({ error: 'Comment not found' });
    }
  } catch (error) {
    logger.error('Failed to delete comment:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete all printed history records from a user
app.delete('/api/comments/user/:username', async (req, res) => {
  try {
    const { username } = req.params;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    // Delete from printed_history table (this is what the history page shows)
    const deletedCount = await database.deleteUserPrintedHistory(username);
    logger.info(`Deleted ${deletedCount} printed history records from user: ${username}`);

    // Emit real-time event for history page refresh
    io.emit('printed-history-updated', {
      action: 'bulk-delete',
      username: username,
      deletedCount: deletedCount,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `Deleted ${deletedCount} printed records from user ${username}`,
      deletedCount
    });
  } catch (error) {
    logger.error('Failed to delete user printed history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Restore deleted printed history record
app.post('/api/comments/:id/restore', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Comment ID is required' });
    }

    const restored = await database.restorePrintedHistoryRecord(parseInt(id));

    if (restored) {
      logger.info(`Restored printed history record: ${id}`);

      // Emit real-time event for history page refresh
      io.emit('printed-history-updated', {
        action: 'restore',
        recordId: parseInt(id),
        timestamp: new Date().toISOString()
      });

      res.json({
        success: true,
        message: `Comment restored successfully`,
        id: parseInt(id)
      });
    } else {
      res.status(404).json({ error: 'Comment not found or already active' });
    }
  } catch (error) {
    logger.error('Failed to restore printed history record:', error);
    res.status(500).json({ error: error.message });
  }
});

// Restore all deleted printed history records from a user
app.post('/api/comments/user/:username/restore', async (req, res) => {
  try {
    const { username } = req.params;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    const restoredCount = await database.restoreUserPrintedHistory(username);
    logger.info(`Restored ${restoredCount} printed history records for user: ${username}`);

    // Emit real-time event for history page refresh
    io.emit('printed-history-updated', {
      action: 'bulk-restore',
      username: username,
      restoredCount: restoredCount,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `Restored ${restoredCount} comments for user ${username}`,
      restoredCount
    });
  } catch (error) {
    logger.error('Failed to restore user printed history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test print endpoint
app.post('/api/test-print', async (req, res) => {
  try {
    const result = await printerService.testPrint();
    logger.info('Test print completed successfully via HTTP');
    res.json({ success: true, result });
  } catch (error) {
    logger.error('Test print failed via HTTP:', error);
    res.status(500).json({ error: error.message });
  }
});

// Print Settings API (Unified)
app.get('/api/print-settings', async (req, res) => {
  try {
    const settings = await database.getPrintSettings();
    res.json({ success: true, settings });
  } catch (error) {
    logger.error('Failed to get print settings:', error);
    res.status(500).json({ error: error.message });
  }
});

// General settings endpoint (fallback)
app.get('/api/settings', async (req, res) => {
  try {
    const printSettings = await database.getPrintSettings();
    const autoMessageSettings = await database.getAutoMessageSettings();
    res.json({
      success: true,
      settings: {
        print: printSettings,
        autoMessage: autoMessageSettings
      }
    });
  } catch (error) {
    logger.error('Failed to get general settings:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/print-settings', async (req, res) => {
  try {
    const { settings } = req.body;
    await database.savePrintSettings(settings);

    // Update printer service with new settings
    if (printerService) {
      await printerService.updatePrintSettings(settings);
    }

    res.json({ success: true, message: 'Print settings saved successfully' });
  } catch (error) {
    logger.error('Failed to save print settings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Auto Message Settings API
app.get('/api/auto-message-settings', async (req, res) => {
  try {
    const settings = await database.getAutoMessageSettings();
    res.json({ success: true, settings });
  } catch (error) {
    logger.error('Failed to get auto message settings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cancel Settings API
app.get('/api/cancel-settings', async (req, res) => {
  try {
    // Check if database is initialized
    if (!database) {
      return res.status(503).json({ success: false, error: 'Database not initialized' });
    }

    // Get settings from database
    const settingsStr = await database.getSetting('cancel_settings');
    let settings = {
      enabled: true,
      duration: 1500,
      enableForPrint: true,
      enableForBackup: true
    };

    if (settingsStr) {
      try {
        settings = { ...settings, ...JSON.parse(settingsStr) };
      } catch (parseError) {
        logger.error('Failed to parse cancel settings from database:', parseError);
      }
    }

    res.json({ success: true, settings });
  } catch (error) {
    logger.error('Failed to get cancel settings:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Save cancel button settings
app.post('/api/cancel-settings', async (req, res) => {
  try {
    // Check if database is initialized
    if (!database) {
      return res.status(503).json({ success: false, error: 'Database not initialized' });
    }

    const { settings } = req.body;
    if (!settings) {
      return res.status(400).json({ success: false, error: 'No settings provided' });
    }

    // Save settings to database
    await database.saveSetting('cancel_settings', JSON.stringify(settings), 'json', 'Cancel button settings');

    // Emit socket event to notify all connected clients
    io.emit('cancel-settings-updated', { settings });

    res.json({ success: true });
  } catch (error) {
    logger.error('Failed to save cancel settings:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/auto-message-settings', async (req, res) => {
  try {
    const { settings } = req.body;
    await database.saveAutoMessageSettings(settings);

    // Update messenger service with new settings if it's running
    if (instagramMessenger && instagramMessenger.isRunning) {
      if (settings.maxMessagesBeforeRestart !== undefined) {
        instagramMessenger.maxMessagesBeforeRestart = settings.maxMessagesBeforeRestart;
        logger.info(`Updated messenger maxMessagesBeforeRestart to: ${settings.maxMessagesBeforeRestart}`);
      }
    }

    res.json({ success: true, message: 'Auto message settings saved successfully' });
  } catch (error) {
    logger.error('Failed to save auto message settings:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/auto-message-stats', async (req, res) => {
  try {
    const stats = await database.getAutoMessageStats();
    res.json({ success: true, stats });
  } catch (error) {
    logger.error('Failed to get auto message stats:', error);
    res.status(500).json({ error: error.message });
  }
});

// Failed Messages API
app.get('/api/failed-messages', async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;
    const failedMessages = await database.getFailedMessages(parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      failedMessages,
      total: failedMessages.length
    });
  } catch (error) {
    logger.error('Failed to get failed messages:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/failed-messages/:id/resolve', async (req, res) => {
  try {
    const { id } = req.params;
    const { resolvedBy, notes } = req.body;

    await database.markFailedMessageResolved(parseInt(id), resolvedBy, notes);

    res.json({
      success: true,
      message: 'Failed message marked as resolved'
    });
  } catch (error) {
    logger.error('Failed to resolve failed message:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete failed message
app.delete('/api/failed-messages/:id', async (req, res) => {
  try {
    const { id } = req.params;

    await database.deleteFailedMessage(parseInt(id));

    res.json({
      success: true,
      message: 'Failed message deleted successfully'
    });
  } catch (error) {
    logger.error('Failed to delete failed message:', error);
    res.status(500).json({ error: error.message });
  }
});

// Retry failed message
app.post('/api/failed-messages/:id/retry', async (req, res) => {
  try {
    const { id } = req.params;

    // Get failed message details
    const failedMessage = await database.getFailedMessageById(parseInt(id));

    if (!failedMessage) {
      return res.status(404).json({ error: 'Failed message not found' });
    }

    // Add back to message queue
    const messageData = {
      id: `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      comment_id: failedMessage.comment_id || null,
      username: failedMessage.username,
      original_comment: failedMessage.original_comment,
      customer_type: failedMessage.customer_type || 'regular',
      template_name: failedMessage.template_name || null,
      template_type: failedMessage.template_type || 'normal',
      status: 'pending',
      retries: 0,
      max_retries: 3
    };

    await database.addToMessageQueue(messageData);

    // Delete from failed messages
    await database.deleteFailedMessage(parseInt(id));

    res.json({
      success: true,
      message: 'Message added back to queue for retry'
    });
  } catch (error) {
    logger.error('Failed to retry failed message:', error);
    res.status(500).json({ error: error.message });
  }
});

// Bulk delete all failed messages
app.delete('/api/failed-messages/bulk/all', async (req, res) => {
  try {
    const deletedCount = await database.deleteAllFailedMessages();

    res.json({
      success: true,
      message: `Deleted ${deletedCount} failed messages`,
      deletedCount
    });
  } catch (error) {
    logger.error('Failed to bulk delete failed messages:', error);
    res.status(500).json({ error: error.message });
  }
});

// Bulk retry all failed messages
app.post('/api/failed-messages/bulk/retry-all', async (req, res) => {
  try {
    // Get all failed messages
    const failedMessages = await database.getFailedMessages(1000, 0); // Get up to 1000 messages

    let successCount = 0;
    let errorCount = 0;

    for (const failedMessage of failedMessages) {
      try {
        // Add back to message queue
        const messageData = {
          id: `retry_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          comment_id: failedMessage.comment_id || null,
          username: failedMessage.username,
          original_comment: failedMessage.original_comment,
          customer_type: failedMessage.customer_type || 'regular',
          template_name: failedMessage.template_name || null,
          template_type: failedMessage.template_type || 'normal',
          status: 'pending',
          retries: 0,
          max_retries: 3
        };

        await database.addToMessageQueue(messageData);

        // Delete from failed messages
        await database.deleteFailedMessage(failedMessage.id);

        successCount++;
      } catch (error) {
        logger.error(`Failed to retry message ${failedMessage.id}:`, error);
        errorCount++;
      }
    }

    res.json({
      success: true,
      message: `Retried ${successCount} messages, ${errorCount} errors`,
      successCount,
      errorCount,
      totalProcessed: failedMessages.length
    });
  } catch (error) {
    logger.error('Failed to bulk retry failed messages:', error);
    res.status(500).json({ error: error.message });
  }
});

// Redirect root to /web
app.get('/', (req, res) => {
  res.redirect('/web');
});

// Catch-all handler for SPA routes under /web (must be after API routes)
app.get('/web/*', (req, res) => {
  res.sendFile(path.join(__dirname, '../web/build/index.html'));
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  logger.info('Health check endpoint called');
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: !!database,
      mongodb: !!mongoDBService,
      initialized: !!(database && mongoDBService)
    }
  });
});

// Debug endpoint to check services status
app.get('/api/debug/services', (req, res) => {
  logger.info('Debug services endpoint called');
  res.json({
    success: true,
    services: {
      database: {
        initialized: !!database,
        type: database ? database.constructor.name : null
      },
      mongoDBService: {
        initialized: !!mongoDBService,
        connected: mongoDBService ? mongoDBService.isConnected : false,
        type: mongoDBService ? mongoDBService.constructor.name : null
      },
      commentProcessor: !!commentProcessor,
      instagramScraper: !!instagramScraper,
      instagramMessenger: !!instagramMessenger,
      messageQueue: !!messageQueue,
      printerService: !!printerService
    }
  });
});

// MongoDB Atlas API endpoints
app.get('/api/mongodb/settings', async (req, res) => {
  try {
    logger.info('MongoDB settings endpoint called');

    if (!database) {
      logger.error('Database service not initialized');
      return res.status(503).json({ error: 'Database service not ready' });
    }

    const connectionString = await database.getSetting('mongodb_connection_string');
    res.json({
      success: true,
      connectionString: connectionString ? '***masked***' : '', // Mask for security
      hasConnectionString: !!connectionString // Indicate if connection string exists
    });
  } catch (error) {
    logger.error('Failed to get MongoDB settings:', error);
    res.status(500).json({ error: 'Failed to get MongoDB settings' });
  }
});

app.post('/api/mongodb/settings', async (req, res) => {
  try {
    const { connectionString } = req.body;

    if (!connectionString) {
      return res.status(400).json({ error: 'Connection string is required' });
    }

    // Test connection first
    const testResult = await mongoDBService.testConnection(connectionString);
    if (!testResult.success) {
      return res.status(400).json({ error: testResult.error });
    }

    // Save connection string
    await database.saveSetting('mongodb_connection_string', connectionString);

    // Connect to MongoDB
    await mongoDBService.connect(connectionString);

    res.json({ success: true, message: 'MongoDB connected successfully' });
  } catch (error) {
    logger.error('Failed to save MongoDB settings:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/mongodb/test', async (req, res) => {
  try {
    const { connectionString } = req.body;

    if (!connectionString) {
      return res.status(400).json({ error: 'Connection string is required' });
    }

    const result = await mongoDBService.testConnection(connectionString);
    res.json(result);
  } catch (error) {
    logger.error('Failed to test MongoDB connection:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/mongodb/status', async (req, res) => {
  try {
    logger.info('MongoDB status endpoint called');

    if (!mongoDBService) {
      logger.error('MongoDB service not initialized');
      return res.json({
        connected: false,
        error: 'MongoDB service not ready',
        lastSync: null,
        autoSyncEnabled: false
      });
    }

    if (!database) {
      logger.error('Database service not initialized');
      return res.json({
        connected: false,
        error: 'Database service not ready',
        lastSync: null,
        autoSyncEnabled: false
      });
    }

    const isConnected = mongoDBService.isConnected;
    const status = mongoDBService.getStatus();
    const lastSync = await database.getSetting('mongodb_last_sync');

    res.json({
      connected: isConnected,
      status,
      lastSync,
      lastConnected: isConnected ? new Date().toISOString() : null,
      connectionString: isConnected ? mongoDBService.maskConnectionString(mongoDBService.connectionString) : null,
      autoSyncEnabled: MONGODB_SYNC_CONFIG.ENABLED,
      syncInterval: MONGODB_SYNC_CONFIG.AUTO_SYNC_INTERVAL,
      success: true
    });
  } catch (error) {
    logger.error('Failed to get MongoDB status:', error);
    res.json({
      connected: false,
      error: error.message,
      lastSync: null,
      autoSyncEnabled: false,
      success: false
    });
  }
});

app.post('/api/mongodb/disconnect', async (req, res) => {
  try {
    await mongoDBService.disconnect();
    res.json({ success: true, message: 'Disconnected from MongoDB' });
  } catch (error) {
    logger.error('Failed to disconnect from MongoDB:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/mongodb/sync-to-mongo', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    // Get local customers
    const localCustomers = await database.getRegularCustomers();

    // Sync to MongoDB
    const result = await mongoDBService.syncCustomersToMongo(localCustomers);

    // Update last sync time
    await database.saveSetting('mongodb_last_sync', new Date().toISOString());

    res.json({ success: true, count: result.count });
  } catch (error) {
    logger.error('Failed to sync to MongoDB:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/mongodb/sync-from-mongo', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    // Get customers from MongoDB
    const mongoCustomers = await mongoDBService.syncCustomersFromMongo();

    // Perform smart bidirectional sync
    const syncResult = await database.smartSyncWithMongoDB(mongoCustomers);

    // Update last sync time
    await database.saveSetting('mongodb_last_sync', new Date().toISOString());

    res.json({
      success: true,
      ...syncResult,
      message: `Smart sync completed: +${syncResult.addedToLocal} local, +${syncResult.addedToMongo} MongoDB, ~${syncResult.updatedLocal + syncResult.updatedMongo} updated`
    });
  } catch (error) {
    logger.error('Failed to sync from MongoDB:', error);
    res.status(500).json({ error: error.message });
  }
});

// Smart bidirectional sync endpoint
app.post('/api/mongodb/smart-sync', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    logger.info('Manual smart sync requested');

    // Get customers from MongoDB
    const mongoCustomers = await mongoDBService.syncCustomersFromMongo();

    // Get threads from MongoDB
    const mongoThreads = await mongoDBService.syncThreadsFromMongo();

    // Get printed history from MongoDB
    const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();

    // Get send_once history from MongoDB
    const mongoSendOnceHistory = await mongoDBService.syncSendOnceHistoryFromMongo();

    // Perform smart sync for customers
    const customerSyncResult = await database.smartSyncWithMongoDB(mongoCustomers);

    // Perform smart sync for threads
    const threadSyncResult = await database.smartSyncThreadsWithMongoDB(mongoThreads);

    // Perform smart sync for printed history
    const printedHistorySyncResult = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory);

    // Perform smart sync for send_once history
    const sendOnceHistorySyncResult = await database.smartSyncSendOnceHistoryWithMongoDB(mongoSendOnceHistory);

    // Update last sync time
    await database.saveSetting('mongodb_last_sync', new Date().toISOString());

    // Notify connected clients about sync
    io.emit('mongodb-sync-completed', {
      customers: customerSyncResult,
      threads: threadSyncResult,
      printedHistory: printedHistorySyncResult,
      sendOnceHistory: sendOnceHistorySyncResult,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      customers: customerSyncResult,
      threads: threadSyncResult,
      printedHistory: printedHistorySyncResult,
      sendOnceHistory: sendOnceHistorySyncResult,
      message: `Smart sync completed - Customers: +${customerSyncResult.addedToLocal} local, +${customerSyncResult.addedToMongo} MongoDB; Threads: +${threadSyncResult.addedToLocal} local, +${threadSyncResult.addedToMongo} MongoDB; Printed History: +${printedHistorySyncResult.addedToLocal} local, +${printedHistorySyncResult.addedToMongo} MongoDB; Send_once History: +${sendOnceHistorySyncResult.addedToLocal} local, +${sendOnceHistorySyncResult.addedToMongo} MongoDB`
    });
  } catch (error) {
    logger.error('Failed to perform smart sync:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sync customers only endpoint
app.post('/api/mongodb/sync-customers', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    logger.info('Manual customers sync requested');

    // Get customers from MongoDB
    const mongoCustomers = await mongoDBService.syncCustomersFromMongo();

    // Perform smart sync for customers only
    const customerSyncResult = await database.smartSyncWithMongoDB(mongoCustomers);

    // Update last sync time
    await database.saveSetting('mongodb_last_sync', new Date().toISOString());

    res.json({
      success: true,
      customers: customerSyncResult,
      message: `Customers sync completed: +${customerSyncResult.addedToLocal} local, +${customerSyncResult.addedToMongo} MongoDB, ~${customerSyncResult.updatedLocal + customerSyncResult.updatedMongo} updated`
    });
  } catch (error) {
    logger.error('Failed to sync customers:', error);
    res.status(500).json({ error: error.message });
  }
});

// Sync printed history only endpoint
app.post('/api/mongodb/sync-printed-history', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    logger.info('Manual printed history sync requested');

    // Get printed history from MongoDB
    const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();

    // Perform smart sync for printed history only
    const printedHistorySyncResult = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory);

    // Update last sync time
    await database.saveSetting('mongodb_last_sync', new Date().toISOString());

    res.json({
      success: true,
      printedHistory: printedHistorySyncResult,
      message: `Printed history sync completed: +${printedHistorySyncResult.addedToLocal} local, +${printedHistorySyncResult.addedToMongo} MongoDB, ~${printedHistorySyncResult.updatedLocal + printedHistorySyncResult.updatedMongo} updated`
    });
  } catch (error) {
    logger.error('Failed to sync printed history:', error);
    res.status(500).json({ error: error.message });
  }
});

// Debug boolean conversion endpoint
app.get('/api/mongodb/debug-boolean-conversion', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    // Get some deleted records from local
    const localDeleted = await database.allQuery(
      'SELECT id, username, comment_id, is_deleted FROM printed_history WHERE is_deleted = 1 LIMIT 5'
    );

    // Get some deleted records from MongoDB
    const mongoCollection = mongoDBService.db.collection('printed_history');
    const mongoDeleted = await mongoCollection.find({ is_deleted: true }).limit(5).toArray();

    res.json({
      success: true,
      local: localDeleted.map(record => ({
        id: record.id,
        username: record.username,
        comment_id: record.comment_id,
        is_deleted: record.is_deleted,
        is_deleted_type: typeof record.is_deleted,
        boolean_conversion: Boolean(record.is_deleted)
      })),
      mongo: mongoDeleted.map(record => ({
        local_id: record.local_id,
        username: record.username,
        comment_id: record.comment_id,
        is_deleted: record.is_deleted,
        is_deleted_type: typeof record.is_deleted
      }))
    });
  } catch (error) {
    logger.error('Failed to debug boolean conversion:', error);
    res.status(500).json({ error: error.message });
  }
});

// Debug sync counts endpoint
app.get('/api/mongodb/debug-counts', async (req, res) => {
  try {
    if (!mongoDBService.isConnected) {
      return res.status(400).json({ error: 'Not connected to MongoDB' });
    }

    // Get local counts
    const localCustomers = await database.getRegularCustomers(); // Only non-deleted
    const localCustomersAll = await database.getRegularCustomersForSync(); // All including deleted
    const localPrintedHistory = await database.getPrintedHistoryForSync();

    // Get MongoDB counts
    const mongoCustomers = await mongoDBService.syncCustomersFromMongo();
    const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();

    // Filter MongoDB customers by deleted status
    const mongoCustomersActive = mongoCustomers.filter(c => !c.is_deleted);
    const mongoCustomersDeleted = mongoCustomers.filter(c => c.is_deleted);

    // Filter printed history by deleted status
    const localPrintedActive = localPrintedHistory.filter(h => !h.is_deleted);
    const localPrintedDeleted = localPrintedHistory.filter(h => h.is_deleted);
    const mongoPrintedActive = mongoPrintedHistory.filter(h => !h.is_deleted);
    const mongoPrintedDeleted = mongoPrintedHistory.filter(h => h.is_deleted);

    res.json({
      customers: {
        local: {
          active: localCustomers.length,
          total: localCustomersAll.length,
          deleted: localCustomersAll.length - localCustomers.length
        },
        mongo: {
          active: mongoCustomersActive.length,
          total: mongoCustomers.length,
          deleted: mongoCustomersDeleted.length
        }
      },
      printedHistory: {
        local: {
          active: localPrintedActive.length,
          total: localPrintedHistory.length,
          deleted: localPrintedDeleted.length
        },
        mongo: {
          active: mongoPrintedActive.length,
          total: mongoPrintedHistory.length,
          deleted: mongoPrintedDeleted.length
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get debug counts:', error);
    res.status(500).json({ error: error.message });
  }
});


// Performance monitoring endpoint
// Force sync system state endpoint
app.post('/api/sync-system-state', (req, res) => {
  try {
    // Force sync systemState with actual session data
    systemState.totalComments = sessionComments.length;

    // Force emit system state to all clients
    io.emit('system-state', systemState);

    logger.info(`🔄 Force synced system state: totalComments = ${systemState.totalComments}`);

    res.json({
      success: true,
      message: 'System state synced successfully',
      sessionComments: sessionComments.length,
      systemStateTotalComments: systemState.totalComments
    });
  } catch (error) {
    logger.error('Failed to sync system state:', error);
    res.status(500).json({ error: error.message });
  }
});

// Debug queue status endpoint
app.get('/api/debug/queue-status', async (req, res) => {
  try {
    const messengerStatus = instagramMessenger ? instagramMessenger.getStatus() : null;
    const queueStats = database ? await database.getMessageQueueStats() : null;
    const queueMessages = database ? await database.getMessageQueue('all', 10) : [];

    res.json({
      success: true,
      debug: {
        messengerRunning: messengerStatus?.isRunning || false,
        messengerLoggedIn: messengerStatus?.isLoggedIn || false,
        isProcessingQueue: messengerStatus?.isProcessingQueue || false,
        queueStats,
        recentMessages: queueMessages.map(msg => ({
          id: msg.id,
          username: msg.username,
          status: msg.status,
          created_at: msg.created_at,
          updated_at: msg.updated_at
        })),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get debug queue status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Remove unused tables endpoint
app.post('/api/database/remove-unused-tables', async (req, res) => {
  try {
    await database.removeUnusedTables();

    res.json({
      success: true,
      message: 'Unused tables removal completed'
    });
  } catch (error) {
    logger.error('Failed to remove unused tables:', error);
    res.status(500).json({ error: 'Failed to remove unused tables' });
  }
});



// Price Mappings API endpoints
app.get('/api/price-mappings', async (req, res) => {
  try {
    const mappings = await database.getAllPriceMappings();
    res.json({
      success: true,
      mappings,
      total: mappings.length
    });
  } catch (error) {
    logger.error('Failed to get price mappings:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/price-mappings', async (req, res) => {
  try {
    const { prefix, price, description, is_active } = req.body;

    if (!prefix || !price) {
      return res.status(400).json({ error: 'Prefix and price are required' });
    }

    await database.savePriceMapping({
      prefix: prefix.toLowerCase(),
      price,
      description: description || '',
      is_active: is_active !== false
    });

    res.json({ success: true, message: 'Price mapping created successfully' });
  } catch (error) {
    logger.error('Failed to create price mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/price-mappings/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { prefix, price, description, is_active } = req.body;

    if (!prefix || !price) {
      return res.status(400).json({ error: 'Prefix and price are required' });
    }

    await database.savePriceMapping({
      id,
      prefix: prefix.toLowerCase(),
      price,
      description: description || '',
      is_active: Boolean(is_active)
    });

    res.json({ success: true, message: 'Price mapping updated successfully' });
  } catch (error) {
    logger.error('Failed to update price mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/price-mappings/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const changes = await database.deletePriceMapping(id);

    if (changes > 0) {
      res.json({ success: true, message: 'Price mapping deleted successfully' });
    } else {
      res.status(404).json({ error: 'Price mapping not found' });
    }
  } catch (error) {
    logger.error('Failed to delete price mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// API endpoint để xóa bình luận phiên
app.post('/api/clear-session-comments', (req, res) => {
  try {
    const commentCount = global.sessionComments.length;
    global.sessionComments = [];
    systemState.totalComments = 0;

    logger.info(`🧹 Cleared ${commentCount} session comments`);

    // Emit system state update
    io.emit('system-state', systemState);

    // Emit event to notify about cleared comments
    io.emit('session-comments-cleared');

    res.json({
      success: true,
      message: `Cleared ${commentCount} session comments`,
      clearedCount: commentCount
    });
  } catch (error) {
    logger.error('Failed to clear session comments:', error);
    res.status(500).json({ error: 'Failed to clear session comments' });
  }
});

// Catch-all handler for other routes - redirect to /web
app.get('*', (req, res) => {
  // Only redirect non-API routes
  if (!req.path.startsWith('/api/')) {
    res.redirect('/web');
  } else {
    res.status(404).json({ error: 'API endpoint not found' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Thiết lập việc tự động dọn dẹp tin nhắn đã hoàn thành trong hàng đợi
function setupCompletedMessageCleanup() {
  // Dọn dẹp các tin nhắn đã hoàn thành cứ mỗi 2 phút
  const CLEANUP_INTERVAL = 2 * 60 * 1000; // 2 phút

  setInterval(async () => {
    try {
      logger.info("Đang dọn dẹp tin nhắn đã hoàn thành...");
      const minutesToKeep = 10; // Giữ tin nhắn đã hoàn thành trong 10 phút
      const deleted = await database.cleanupCompletedMessages(minutesToKeep);
      if (deleted > 0) {
        logger.info(`Đã xóa ${deleted} tin nhắn đã hoàn thành cách đây hơn ${minutesToKeep} phút`);
        io.emit('message-queue-updated'); // Thông báo cho client cập nhật giao diện
      }
    } catch (error) {
      logger.error("Lỗi khi dọn dẹp tin nhắn đã hoàn thành:", error);
    }
  }, CLEANUP_INTERVAL);

  logger.info(`Lịch dọn dẹp tin nhắn đã hoàn thành được thiết lập mỗi ${CLEANUP_INTERVAL / (60 * 1000)} phút`);
}

// Setup automatic send_once history cleanup
setupSendOnceHistoryCleanup();

// Setup automatic sent messages cleanup
function setupSentMessagesCleanup() {
  const SENT_MESSAGES_CLEANUP_INTERVAL = 5 * 60 * 1000; // Clean up every 5 minutes

  logger.info('Setting up automatic sent messages cleanup');

  // Initial cleanup
  setTimeout(async () => {
    try {
      logger.info('Running initial sent messages cleanup');
      await database.cleanupOldSentMessages(10); // Clean messages older than 10 minutes
    } catch (error) {
      logger.error('Error during initial sent messages cleanup:', error);
    }
  }, 30 * 1000); // Wait 30 seconds after startup

  // Periodic cleanup
  setInterval(async () => {
    try {
      logger.info('Running scheduled sent messages cleanup');
      const deleted = await database.cleanupOldSentMessages(10);
      logger.info(`Scheduled cleanup completed: ${deleted} old sent messages removed`);
    } catch (error) {
      logger.error('Error during scheduled sent messages cleanup:', error);
    }
  }, SENT_MESSAGES_CLEANUP_INTERVAL);

  logger.info(`Sent messages cleanup scheduled every ${SENT_MESSAGES_CLEANUP_INTERVAL / (60 * 1000)} minutes`);
}

// Setup automatic completed message cleanup
function setupCompletedMessageCleanup() {
  // Dọn dẹp các tin nhắn đã hoàn thành cứ mỗi 2 phút
  const CLEANUP_INTERVAL = 2 * 60 * 1000; // 2 phút

  setInterval(async () => {
    try {
      logger.info("Đang dọn dẹp tin nhắn đã hoàn thành...");
      const minutesToKeep = 10; // Giữ tin nhắn đã hoàn thành trong 10 phút
      const deleted = await database.cleanupCompletedMessages(minutesToKeep);
      if (deleted > 0) {
        logger.info(`Đã xóa ${deleted} tin nhắn đã hoàn thành cách đây hơn ${minutesToKeep} phút`);
        io.emit('message-queue-updated'); // Thông báo cho client cập nhật giao diện
      }
    } catch (error) {
      logger.error("Lỗi khi dọn dẹp tin nhắn đã hoàn thành:", error);
    }
  }, CLEANUP_INTERVAL);

  logger.info(`Lịch dọn dẹp tin nhắn đã hoàn thành được thiết lập mỗi ${CLEANUP_INTERVAL / (60 * 1000)} phút`);
}

// Start server
const PORT = process.env.PORT || 3001;

async function startServer() {
  try {
    await initializeServices();

    server.listen(PORT, '0.0.0.0', () => {
      logger.info(`Server running on port ${PORT}`);
      console.log(`Instagram Live Comment System Server started on port ${PORT}`);

      // Get local IP address for mobile access - prioritize 192.168.x.x
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();
      const localIPs = [];

      Object.keys(networkInterfaces).forEach(interfaceName => {
        networkInterfaces[interfaceName].forEach(interface => {
          if (interface.family === 'IPv4' && !interface.internal) {
            localIPs.push(interface.address);
          }
        });
      });

      if (localIPs.length > 0) {
        // Prioritize 192.168.x.x addresses (local network)
        let preferredIP = localIPs.find(ip => ip.startsWith('192.168.'));

        // Fallback to 10.x.x.x addresses
        if (!preferredIP) {
          preferredIP = localIPs.find(ip => ip.startsWith('10.'));
        }

        // Fallback to 172.16-31.x.x addresses
        if (!preferredIP) {
          preferredIP = localIPs.find(ip => {
            const parts = ip.split('.');
            return parts[0] === '172' && parseInt(parts[1]) >= 16 && parseInt(parts[1]) <= 31;
          });
        }

        // Use preferred IP or first available
        const displayIP = preferredIP || localIPs[0];

        console.log(`\n📱 For mobile access, use: http://${displayIP}:${PORT}/web`);
        console.log(`🌐 Local access: http://localhost:${PORT}/web\n`);
      }
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Removed database cleanup functions - no longer needed

// Setup periodic MongoDB auto-sync
let mongoSyncInterval;
function setupMongoDBAutoSync() {
  // Clear existing interval if any
  if (mongoSyncInterval) {
    clearInterval(mongoSyncInterval);
  }

  if (!MONGODB_SYNC_CONFIG.ENABLED) {
    logger.info('MongoDB auto-sync is disabled');
    return;
  }

  // Setup new sync interval
  mongoSyncInterval = setInterval(async () => {
    try {
      if (mongoDBService && mongoDBService.isConnected) {
        logger.info('Starting periodic MongoDB sync...');

        // Get customers from MongoDB
        const mongoCustomers = await mongoDBService.syncCustomersFromMongo();

        // Get threads from MongoDB
        const mongoThreads = await mongoDBService.syncThreadsFromMongo();

        // Get printed history from MongoDB
        const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();

        // Get send_once history from MongoDB
        const mongoSendOnceHistory = await mongoDBService.syncSendOnceHistoryFromMongo();

        // Perform smart sync for customers
        const customerSyncResult = await database.smartSyncWithMongoDB(mongoCustomers);

        // Perform smart sync for threads
        const threadSyncResult = await database.smartSyncThreadsWithMongoDB(mongoThreads);

        // Perform smart sync for printed history
        const printedHistorySyncResult = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory);

        // Perform smart sync for send_once history
        const sendOnceHistorySyncResult = await database.smartSyncSendOnceHistoryWithMongoDB(mongoSendOnceHistory);

        const totalChanges = customerSyncResult.addedToLocal + customerSyncResult.addedToMongo +
          customerSyncResult.updatedLocal + customerSyncResult.updatedMongo +
          threadSyncResult.addedToLocal + threadSyncResult.addedToMongo +
          threadSyncResult.updatedLocal + threadSyncResult.updatedMongo +
          printedHistorySyncResult.addedToLocal + printedHistorySyncResult.addedToMongo +
          printedHistorySyncResult.updatedLocal + printedHistorySyncResult.updatedMongo +
          sendOnceHistorySyncResult.addedToLocal + sendOnceHistorySyncResult.addedToMongo +
          sendOnceHistorySyncResult.updatedLocal + sendOnceHistorySyncResult.updatedMongo;

        if (totalChanges > 0) {
          logger.info(`Periodic MongoDB sync completed:`);
          logger.info(`  Customers: +${customerSyncResult.addedToLocal} local, +${customerSyncResult.addedToMongo} MongoDB, ~${customerSyncResult.updatedLocal + customerSyncResult.updatedMongo} updated`);
          logger.info(`  Threads: +${threadSyncResult.addedToLocal} local, +${threadSyncResult.addedToMongo} MongoDB, ~${threadSyncResult.updatedLocal + threadSyncResult.updatedMongo} updated`);
          logger.info(`  Printed History: +${printedHistorySyncResult.addedToLocal} local, +${printedHistorySyncResult.addedToMongo} MongoDB, ~${printedHistorySyncResult.updatedLocal + printedHistorySyncResult.updatedMongo} updated`);
          logger.info(`  Send_once History: +${sendOnceHistorySyncResult.addedToLocal} local, +${sendOnceHistorySyncResult.addedToMongo} MongoDB, ~${sendOnceHistorySyncResult.updatedLocal + sendOnceHistorySyncResult.updatedMongo} updated`);

          // Update last sync time
          await database.saveSetting('mongodb_last_sync', new Date().toISOString());

          // Notify connected clients about sync
          io.emit('mongodb-sync-completed', {
            customers: customerSyncResult,
            threads: threadSyncResult,
            printedHistory: printedHistorySyncResult,
            sendOnceHistory: sendOnceHistorySyncResult,
            timestamp: new Date().toISOString()
          });
        } else {
          logger.debug('Periodic MongoDB sync: No changes detected');
        }
      }
    } catch (error) {
      logger.error('Periodic MongoDB sync failed:', error);
    }
  }, MONGODB_SYNC_CONFIG.AUTO_SYNC_INTERVAL);

  logger.info(`MongoDB auto-sync scheduled every ${MONGODB_SYNC_CONFIG.AUTO_SYNC_INTERVAL / 60000} minutes`);
}

// Kiểm tra và dọn dẹp send_once_history nếu cần
async function setupSendOnceHistoryCleanup() {
  try {
    logger.info('🔍 Kiểm tra dữ liệu send_once cũ...');

    // Kiểm tra xem đã dọn dẹp lần cuối khi nào
    const lastCleanupTime = await database.getSetting('last_sendonce_cleanup_time');

    if (!lastCleanupTime) {
      // Chưa có lần dọn dẹp nào, thực hiện ngay
      logger.info('🧹 Chưa có lần dọn dẹp nào được ghi nhận, thực hiện dọn dẹp send_once history ngay...');
      const deletedCount = await database.cleanupOldSendOnceHistory(2); // 2 days

      if (deletedCount > 0) {
        logger.info(`✅ Đã xóa ${deletedCount} bản ghi send_once history cũ hơn 2 ngày`);

        // Thông báo cho client
        io.emit('send-once-cleanup-completed', {
          deletedCount,
          cleanupDays: 2,
          timestamp: new Date().toISOString()
        });
      } else {
        logger.debug('Không tìm thấy bản ghi cũ nào cần xóa');
      }
    } else {
      // Kiểm tra xem đã qua 2 ngày kể từ lần dọn dẹp cuối chưa
      const lastCleanup = new Date(lastCleanupTime);
      const now = new Date();
      const daysSinceLastCleanup = (now - lastCleanup) / (1000 * 60 * 60 * 24);

      if (daysSinceLastCleanup >= 2) {
        logger.info(`🧹 Đã ${daysSinceLastCleanup.toFixed(1)} ngày kể từ lần dọn dẹp cuối (${lastCleanupTime}), thực hiện dọn dẹp send_once history...`);

        const deletedCount = await database.cleanupOldSendOnceHistory(2); // 2 days

        if (deletedCount > 0) {
          logger.info(`✅ Đã xóa ${deletedCount} bản ghi send_once history cũ hơn 2 ngày`);

          // Thông báo cho client
          io.emit('send-once-cleanup-completed', {
            deletedCount,
            cleanupDays: 2,
            timestamp: new Date().toISOString()
          });
        } else {
          logger.debug('Không tìm thấy bản ghi cũ nào cần xóa');
        }
      } else {
        logger.info(`🕒 Chưa đến thời gian dọn dẹp, lần cuối dọn dẹp: ${lastCleanupTime} (${daysSinceLastCleanup.toFixed(1)} ngày trước)`);
      }
    }

    logger.info('Đã thiết lập cơ chế dọn dẹp send_once history tự động dựa trên thời gian');
  } catch (error) {
    logger.error('Không thể kiểm tra/dọn dẹp send_once history:', error);
  }
}

// Setup automatic cleanup for printed_history (every 24 hours)


// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  // Clear MongoDB sync interval
  if (mongoSyncInterval) {
    clearInterval(mongoSyncInterval);
  }


  if (instagramScraper) {
    await instagramScraper.stop();
  }

  if (instagramMessenger) {
    await instagramMessenger.stop();
  }

  if (messageQueue) {
    await messageQueue.close();
  }

  if (database) {
    await database.close();
  }

  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  // Clear MongoDB sync interval
  if (mongoSyncInterval) {
    clearInterval(mongoSyncInterval);
  }

  if (instagramScraper) {
    await instagramScraper.stop();
  }

  if (instagramMessenger) {
    await instagramMessenger.stop();
  }

  if (messageQueue) {
    await messageQueue.close();
  }

  if (database) {
    await database.close();
  }

  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Start the server
startServer();

// Message Queue API endpoints
app.get('/api/message-queue/next', async (req, res) => {
  try {
    logger.info('Fetching next message from queue...');
    // Lấy tin nhắn tiếp theo từ hàng đợi
    const nextMessage = await database.getNextMessageFromQueue();

    if (nextMessage) {
      logger.info(`Found message in queue: ${nextMessage.id} for @${nextMessage.username}`);
      // Đánh dấu tin nhắn là đang xử lý
      await database.updateMessageStatus(nextMessage.id, 'processing');

      // Emit socket event để frontend cập nhật realtime
      io.emit('message-status-update', { id: nextMessage.id, status: 'processing' });
      logger.info(`Emitted processing status for message: ${nextMessage.id}`);

      res.json({
        success: true,
        message: nextMessage
      });
    } else {
      res.json({
        success: true,
        message: null
      });
    }
  } catch (error) {
    logger.error('Failed to get next message from queue:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API kiểm tra trạng thái dọn dẹp send_once history
app.get('/api/send-once-history/cleanup-status', async (req, res) => {
  try {
    const lastCleanupTime = await database.getSetting('last_sendonce_cleanup_time');

    if (!lastCleanupTime) {
      return res.json({
        success: true,
        lastCleanup: null,
        daysSinceLastCleanup: null,
        message: 'Chưa có lần dọn dẹp nào được ghi nhận'
      });
    }

    const lastCleanup = new Date(lastCleanupTime);
    const now = new Date();
    const daysSinceLastCleanup = (now - lastCleanup) / (1000 * 60 * 60 * 24);

    res.json({
      success: true,
      lastCleanup: lastCleanupTime,
      daysSinceLastCleanup: daysSinceLastCleanup.toFixed(1),
      message: `Lần dọn dẹp cuối: ${lastCleanupTime} (${daysSinceLastCleanup.toFixed(1)} ngày trước)`
    });
  } catch (error) {
    logger.error('Failed to get cleanup status:', error);
    res.status(500).json({ error: 'Failed to get cleanup status' });
  }
});

// Thêm API endpoint để lấy tin nhắn tiếp theo từ hàng đợi
app.get('/api/message-queue/next', async (req, res) => {
  try {
    logger.info('Fetching next message from queue...');
    // Lấy tin nhắn tiếp theo từ hàng đợi
    const nextMessage = await database.getNextMessageFromQueue();

    if (nextMessage) {
      logger.info(`Found message in queue: ${nextMessage.id} for @${nextMessage.username}`);
      // Đánh dấu tin nhắn là đang xử lý
      await database.updateMessageStatus(nextMessage.id, 'processing');

      // Emit socket event để frontend cập nhật realtime
      io.emit('message-status-update', { id: nextMessage.id, status: 'processing' });
      logger.info(`Emitted processing status for message: ${nextMessage.id}`);

      res.json({
        success: true,
        message: nextMessage
      });
    } else {
      res.json({
        success: true,
        message: null
      });
    }
  } catch (error) {
    logger.error('Failed to get next message from queue:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint để kiểm tra và xác nhận template
app.get('/api/message-queue/validate-templates', async (req, res) => {
  try {
    logger.info('Validating message queue templates...');

    // Lấy tất cả tin nhắn trong hàng đợi kèm thông tin template
    const messages = await database.allQuery(
      `SELECT mq.id, mq.username, mq.customer_type, mq.template_name, mq.status,
              mt.id as template_id, mt.name as actual_template_name, 
              mt.customer_type as actual_customer_type
       FROM message_queue mq
       LEFT JOIN message_templates mt 
       ON mq.template_name = mt.name AND mq.customer_type = mt.customer_type
       WHERE mq.status = 'pending'`
    );

    // Tìm các tin nhắn không khớp template hoặc loại người dùng
    const mismatches = messages.filter(m =>
      m.template_name && (!m.template_id || m.customer_type !== m.actual_customer_type)
    );

    // Nếu có tin nhắn không khớp, ghi log chi tiết
    if (mismatches.length > 0) {
      logger.warn(`Found ${mismatches.length} messages with mismatched templates!`);
      mismatches.forEach(msg => {
        logger.warn(`Message ${msg.id} for @${msg.username} has template "${msg.template_name}" with customer type "${msg.customer_type}", but template exists with customer type "${msg.actual_customer_type || 'NONE'}"`)
      });
    } else {
      logger.info(`All ${messages.length} messages have valid templates.`);
    }

    res.json({
      success: true,
      total: messages.length,
      mismatches: mismatches.length,
      mismatchedMessages: mismatches
    });
  } catch (error) {
    logger.error('Failed to validate templates:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API endpoint để kiểm tra chi tiết tin nhắn trong hàng đợi
app.get('/api/message-queue/details', async (req, res) => {
  try {
    logger.info('Getting message queue details...');

    // Lấy chi tiết từng tin nhắn trong hàng đợi
    const messages = await database.allQuery(
      `SELECT mq.id, mq.username, mq.customer_type, mq.template_name, mq.status, mq.message,
              mt.template as template_content
       FROM message_queue mq
       LEFT JOIN message_templates mt 
       ON mq.template_name = mt.name AND mq.customer_type = mt.customer_type
       ORDER BY mq.created_at ASC`
    );

    // Thống kê theo trạng thái và loại người dùng
    const stats = {
      total: messages.length,
      byStatus: {},
      byCustomerType: {}
    };

    messages.forEach(msg => {
      // Thống kê theo trạng thái
      stats.byStatus[msg.status] = (stats.byStatus[msg.status] || 0) + 1;

      // Thống kê theo loại người dùng
      stats.byCustomerType[msg.customer_type] = (stats.byCustomerType[msg.customer_type] || 0) + 1;
    });

    res.json({
      success: true,
      messages,
      stats
    });
  } catch (error) {
    logger.error('Failed to get message queue details:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API endpoint để thu hồi tin nhắn đã gửi qua Instagram
app.post('/api/instagram/unsend', async (req, res) => {
  try {
    const { thread_id, message_id } = req.body;

    if (!thread_id || !message_id) {
      return res.status(400).json({
        success: false,
        error: 'Thiếu thông tin thread_id hoặc message_id'
      });
    }

    logger.info(`Thu hồi tin nhắn: thread_id=${thread_id}, message_id=${message_id}`);

    // Kiểm tra xem có InstagrapiMessenger service không
    if (!global.instagrapiMessenger) {
      throw new Error('InstagrapiMessenger service không có sẵn');
    }

    // Gọi API unsend_message của InstagrapiService
    const result = await global.instagrapiMessenger.unsendMessage(thread_id, message_id);

    if (result.status === 'success') {
      logger.info('Thu hồi tin nhắn thành công');
      res.json({
        success: true,
        message: 'Đã thu hồi tin nhắn thành công'
      });
    } else {
      throw new Error(result.message || 'Không thể thu hồi tin nhắn');
    }
  } catch (error) {
    logger.error('Failed to unsend message:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Debug endpoint để kiểm tra mối quan hệ giữa message và printed_history
app.get('/api/debug/message-history/:messageId', async (req, res) => {
  try {
    const { messageId } = req.params;

    // Tìm trong queue
    const queueMessage = await database.getMessageById(messageId);

    // Tìm trong sent_messages
    const sentMessage = await database.getSentMessage({ instagram_message_id: messageId });

    // Tìm trong printed_history
    let printedRecords = [];
    if (queueMessage && queueMessage.comment_id) {
      printedRecords = await database.allQuery('SELECT * FROM printed_history WHERE comment_pk = ? AND is_deleted = 0', [queueMessage.comment_id]);
    }

    // Tìm theo username nếu có
    let printedByUsername = [];
    if (sentMessage) {
      printedByUsername = await database.allQuery('SELECT * FROM printed_history WHERE username = ? AND is_deleted = 0 ORDER BY printed_at DESC LIMIT 5', [sentMessage.username]);
    }

    res.json({
      success: true,
      debug: {
        messageId,
        queueMessage,
        sentMessage,
        printedRecords,
        printedByUsername,
        relationships: {
          hasQueueMessage: !!queueMessage,
          hasSentMessage: !!sentMessage,
          queueCommentId: queueMessage?.comment_id,
          printedRecordsCount: printedRecords.length,
          printedByUsernameCount: printedByUsername.length
        }
      }
    });
  } catch (error) {
    logger.error('Debug message history error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Add API endpoint for unsending messages
app.delete('/api/instagram/unsend/:messageId', async (req, res) => {
  try {
    const { messageId } = req.params;
    if (!messageId) {
      return res.status(400).json({ error: 'Message ID is required' });
    }

    logger.info(`Thu hồi tin nhắn request: messageId=${messageId}`);

    // Bước 1: Tìm message trong queue (có thể là pending hoặc processing)
    let queueMessage = null;
    if (database.getMessageById) {
      queueMessage = await database.getMessageById(messageId);
      if (queueMessage) {
        logger.info(`Found queue message: id=${queueMessage.id}, status=${queueMessage.status}, comment_id=${queueMessage.comment_id}`);
      }
    }

    // Bước 2: Tìm trong sent_messages (cho completed messages)
    let sentMessage = await database.getSentMessage({ instagram_message_id: messageId });
    if (sentMessage) {
      logger.info(`Found sent message: id=${sentMessage.id}, instagram_message_id=${sentMessage.instagram_message_id}`);
    }

    // TRƯỜNG HỢP 1: Message đang ở trạng thái chờ (pending) - chỉ xóa khỏi queue
    if (queueMessage && queueMessage.status === 'pending') {
      logger.info(`Processing PENDING unsend for queue message: ${queueMessage.id}`);

      // Xóa khỏi queue
      await database.removeFromMessageQueue(queueMessage.id);
      logger.info(`Removed message from queue: ${queueMessage.id}`);

      // Xóa khỏi printed_history nếu có
      let deletedHistoryCount = 0;
      if (queueMessage.comment_id) {
        const printed = await database.allQuery('SELECT id FROM printed_history WHERE comment_pk = ? AND is_deleted = 0', [queueMessage.comment_id]);
        for (const p of printed) {
          await database.deletePrintedHistoryRecord(p.id);
          deletedHistoryCount++;
        }
        logger.info(`Deleted ${deletedHistoryCount} printed_history records for comment_pk: ${queueMessage.comment_id}`);
      }

      // Emit events để frontend cập nhật
      io.emit('message-queue-updated');
      io.emit('printed-history-updated', {
        action: 'unsend-pending',
        username: queueMessage.username,
        deletedCount: deletedHistoryCount,
        timestamp: new Date().toISOString()
      });

      return res.json({
        success: true,
        message: 'Message removed from queue (pending)',
        type: 'pending',
        deletedHistoryRecords: deletedHistoryCount
      });
    }

    // TRƯỜNG HỢP 2: Message đã gửi (completed) - cần gọi API Instagrapi
    if (sentMessage) {
      logger.info(`Processing COMPLETED unsend for sent message: ${sentMessage.instagram_message_id}`);

      // Gọi Instagrapi unsend API
      try {
        const response = await axios.post('http://localhost:5000/unsend_message', {
          thread_id: sentMessage.thread_id,
          message_id: sentMessage.instagram_message_id
        });
        if (response.data.status === 'success') {
          await database.updateSentMessageStatus(sentMessage.id, 'unsent');
          await database.deleteSentMessage(sentMessage.id);
          if (sentMessage.queue_message_id) {
            await database.removeFromMessageQueue(sentMessage.queue_message_id);
          }

          // Xóa khỏi printed_history - thử nhiều cách để tìm record
          let deletedCount = 0;

          // Cách 1: Dựa vào comment_id từ queue message
          if (queueMessage && queueMessage.comment_id) {
            const printed1 = await database.allQuery('SELECT id FROM printed_history WHERE comment_pk = ? AND is_deleted = 0', [queueMessage.comment_id]);
            for (const p of printed1) {
              await database.deletePrintedHistoryRecord(p.id);
              deletedCount++;
            }
            logger.info(`Deleted ${printed1.length} printed_history records by comment_pk = ${queueMessage.comment_id}`);
          }

          // Cách 2: Dựa vào username và message content (fallback)
          if (deletedCount === 0) {
            const printed2 = await database.allQuery(
              'SELECT id FROM printed_history WHERE username = ? AND comment_text = ? AND is_deleted = 0',
              [sentMessage.username, sentMessage.message_content]
            );
            for (const p of printed2) {
              await database.deletePrintedHistoryRecord(p.id);
              deletedCount++;
            }
            logger.info(`Deleted ${printed2.length} printed_history records by username + content match`);
          }

          // Cách 3: Dựa vào username và thời gian gần đây (last resort)
          if (deletedCount === 0) {
            const recentTime = new Date(Date.now() - 10 * 60 * 1000).toISOString(); // 10 phút trước
            const printed3 = await database.allQuery(
              'SELECT id FROM printed_history WHERE username = ? AND printed_at > ? AND is_deleted = 0 ORDER BY printed_at DESC LIMIT 1',
              [sentMessage.username, recentTime]
            );
            for (const p of printed3) {
              await database.deletePrintedHistoryRecord(p.id);
              deletedCount++;
            }
            logger.info(`Deleted ${printed3.length} printed_history records by username + recent time`);
          }

          logger.info(`Message unsent and deleted: ${sentMessage.instagram_message_id} from @${sentMessage.username}, removed ${deletedCount} history records`);

          // Emit events để frontend cập nhật
          io.emit('message-unsent', {
            id: sentMessage.id,
            instagram_message_id: sentMessage.instagram_message_id,
            username: sentMessage.username
          });

          // Emit event để History page cập nhật
          io.emit('printed-history-updated', {
            action: 'unsend',
            username: sentMessage.username,
            deletedCount: deletedCount,
            timestamp: new Date().toISOString()
          });

          return res.json({
            success: true,
            message: 'Message unsent and deleted successfully',
            type: 'completed',
            deletedHistoryRecords: deletedCount
          });
        } else {
          throw new Error(response.data.message || 'Failed to unsend message via Instagrapi');
        }
      } catch (apiError) {
        logger.error('Failed to unsend message via API:', apiError);
        return res.status(500).json({ success: false, error: apiError.message || 'Failed to call Instagrapi unsend API' });
      }
    }

    // TRƯỜNG HỢP 3: Không tìm thấy message
    logger.warn(`Message not found: messageId=${messageId}, queueMessage=${!!queueMessage}, sentMessage=${!!sentMessage}`);
    return res.status(404).json({
      success: false,
      error: 'Message not found in queue or sent_messages',
      debug: {
        messageId,
        foundInQueue: !!queueMessage,
        foundInSent: !!sentMessage,
        queueStatus: queueMessage?.status
      }
    });
  } catch (error) {
    logger.error('Error unsending message:', error);
    res.status(500).json({ error: error.message });
  }
});

// Endpoint to get sent messages for a username
app.get('/api/sent-messages/:username', async (req, res) => {
  try {
    const { username } = req.params;
    const { limit = 50 } = req.query;

    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }

    const messages = await database.getSentMessagesByUsername(username, parseInt(limit));

    res.json({
      success: true,
      messages,
      total: messages.length
    });
  } catch (error) {
    logger.error('Failed to get sent messages:', error);
    res.status(500).json({ error: error.message });
  }
});
