import React, { useState } from 'react';
import { X, Eye, EyeOff, LogIn } from 'lucide-react';

const LoginPopup = ({
  isOpen,
  onClose,
  onLogin,
  title,
  isLoading = false,
  showLiveUsernameInput = false,
  liveUsername = '',
  onLiveUsernameChange = null,
  startupMode = false // New prop to indicate if this is just for starting service
}) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();

    // If in startup mode, don't require credentials
    if (startupMode) {
      if (showLiveUsernameInput && !liveUsername.trim()) {
        return;
      }
      onLogin({});
      return;
    }

    // Normal login mode - require credentials
    if (!credentials.username.trim() || !credentials.password.trim()) {
      return;
    }
    if (showLiveUsernameInput && !liveUsername.trim()) {
      return;
    }
    onLogin(credentials);
  };

  const handleClose = () => {
    setCredentials({ username: '', password: '' });
    setShowPassword(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Live Username Input (conditional) */}
          {showLiveUsernameInput && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tên Instagram Live
              </label>
              <input
                type="text"
                value={liveUsername}
                onChange={(e) => onLiveUsernameChange && onLiveUsernameChange(e.target.value)}
                placeholder="Nhập tên Instagram Live..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading}
                autoFocus={!liveUsername}
              />
              <p className="text-xs text-gray-500 mt-1">
                Ví dụ: nếu URL là instagram.com/username/live thì nhập "username"
              </p>
            </div>
          )}

          {/* Only show credentials input if not in startup mode */}
          {!startupMode && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tên đăng nhập Instagram
                </label>
                <input
                  type="text"
                  value={credentials.username}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                  placeholder="Nhập tên đăng nhập"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoading}
                  autoFocus={!showLiveUsernameInput || !!liveUsername}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mật khẩu Instagram
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Nhập mật khẩu"
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
            </>
          )}

          {/* Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-700">
              <strong>Lưu ý:</strong> {startupMode
                ? 'Sẽ sử dụng phiên đăng nhập đã lưu để khởi động dịch vụ.'
                : 'Thông tin đăng nhập sẽ được lưu an toàn để sử dụng cho các lần tiếp theo.'
              }
            </p>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              disabled={isLoading}
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={
                isLoading ||
                (!startupMode && (!credentials.username.trim() || !credentials.password.trim())) ||
                (showLiveUsernameInput && !liveUsername.trim())
              }
              className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {startupMode ? 'Đang khởi động...' : 'Đang đăng nhập...'}
                </>
              ) : (
                <>
                  <LogIn className="h-4 w-4 mr-2" />
                  {startupMode ? 'Khởi động' : 'Đăng nhập'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPopup;
