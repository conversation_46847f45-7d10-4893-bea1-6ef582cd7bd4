<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Counter Logic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .counters {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .counter {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 4px;
            min-width: 100px;
        }
        .counter-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .counter-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        button:hover { opacity: 0.8; }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Counter Logic</h1>
        
        <div class="counters">
            <div class="counter">
                <div class="counter-value" id="printed-count">0</div>
                <div class="counter-label">Đã in</div>
            </div>
            <div class="counter">
                <div class="counter-value" id="queued-count">0</div>
                <div class="counter-label">Chờ</div>
            </div>
            <div class="counter">
                <div class="counter-value" id="sent-count">0</div>
                <div class="counter-label">Đã gửi</div>
            </div>
        </div>

        <div class="status" id="status">
            Kết nối với server...
        </div>

        <div class="actions">
            <button class="btn-primary" onclick="connectSocket()">Kết nối Socket</button>
            <button class="btn-info" onclick="loadSystemState()">Load System State</button>
            <button class="btn-success" onclick="simulatePrint()">Giả lập In</button>
            <button class="btn-warning" onclick="simulateQueue()">Giả lập Queue</button>
            <button class="btn-success" onclick="simulateCompleted()">Giả lập Hoàn thành</button>
            <button class="btn-danger" onclick="simulateUnsendPending()">Thu hồi Pending</button>
            <button class="btn-danger" onclick="simulateUnsendCompleted()">Thu hồi Completed</button>
            <button class="btn-info" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="log" id="log">
            <div>Log messages will appear here...</div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let systemState = {
            printedComments: 0,
            queuedMessages: 0,
            sentMessages: 0
        };

        function log(message) {
            const logEl = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${time}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
        }

        function updateCounters() {
            document.getElementById('printed-count').textContent = systemState.printedComments;
            document.getElementById('queued-count').textContent = systemState.queuedMessages;
            document.getElementById('sent-count').textContent = systemState.sentMessages;
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function connectSocket() {
            if (socket) {
                socket.disconnect();
            }
            
            socket = io('http://localhost:3001');
            
            socket.on('connect', () => {
                log('✅ Socket connected: ' + socket.id);
                updateStatus('Đã kết nối với server');
                loadSystemState();
            });

            socket.on('disconnect', () => {
                log('❌ Socket disconnected');
                updateStatus('Mất kết nối với server');
            });

            socket.on('system-state', (data) => {
                log('📊 System state update: ' + JSON.stringify({
                    printed: data.printedComments,
                    queued: data.queuedMessages,
                    sent: data.sentMessages
                }));
                
                systemState.printedComments = data.printedComments || 0;
                systemState.queuedMessages = data.queuedMessages || 0;
                systemState.sentMessages = data.sentMessages || 0;
                updateCounters();
            });

            socket.on('print-success', (data) => {
                log('🖨️ Print success: ' + JSON.stringify(data));
            });

            socket.on('message-sent', (data) => {
                log('📤 Message sent: ' + JSON.stringify(data));
            });

            socket.on('message-queued', (data) => {
                log('📥 Message queued: ' + JSON.stringify(data));
            });

            socket.on('message-unsent', (data) => {
                log('↩️ Message unsent: ' + JSON.stringify(data));
            });
        }

        async function loadSystemState() {
            try {
                const response = await fetch('http://localhost:3001/api/stats');
                const data = await response.json();
                
                log('📈 Loaded system state: ' + JSON.stringify({
                    printed: data.printedComments,
                    queued: data.queuedMessages,
                    sent: data.sentMessages
                }));
                
                systemState.printedComments = data.printedComments || 0;
                systemState.queuedMessages = data.queuedMessages || 0;
                systemState.sentMessages = data.sentMessages || 0;
                updateCounters();
                updateStatus('System state loaded');
            } catch (error) {
                log('❌ Failed to load system state: ' + error.message);
                updateStatus('Lỗi load system state');
            }
        }

        function simulatePrint() {
            if (!socket) {
                log('❌ No socket connection');
                return;
            }
            
            const comment = {
                id: 'test_' + Date.now(),
                username: 'test_user',
                text: 'Test comment for printing'
            };
            
            log('🖨️ Simulating print for: ' + comment.username);
            socket.emit('print-comment', comment, 'print_notification');
        }

        function simulateQueue() {
            log('📥 Simulating message queue (this would happen automatically after print)');
        }

        function simulateCompleted() {
            log('✅ Simulating message completed (this would happen when message is sent)');
        }

        function simulateUnsendPending() {
            log('↩️ Simulating unsend pending message (would decrease queued count)');
        }

        function simulateUnsendCompleted() {
            log('↩️ Simulating unsend completed message (would decrease sent count)');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Page loaded - ready to test counter logic');
            updateCounters();
        });
    </script>
</body>
</html>
