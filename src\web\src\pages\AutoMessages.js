import React, { useEffect, useState } from 'react';
import { useApp } from '../contexts/AppContext';
import MessageTemplates from '../components/MessageTemplates';
import PriceMappings from '../components/PriceMappings';
import MessengerControl from '../components/MessengerControl';
import AutoMessageControl from '../components/AutoMessageControl';
import FailedMessages from '../components/FailedMessages';
import MessageQueueManager from '../components/MessageQueueManager';
import MessengerSwitcher from '../components/MessengerSwitcher';
import InstagrapiSettings from '../components/InstagrapiSettings';
import { MessageSquare } from 'lucide-react';

const LOCAL_STORAGE_KEY = 'messenger_mode_instagrapi';

const AutoMessages = () => {
  const { actions } = useApp();
  const [useInstagrapi, setUseInstagrapi] = useState(false);

  useEffect(() => {
    actions.setCurrentPage('auto-messages');

    // Đọc trạng thái từ localStorage khi component mount
    const savedMode = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedMode !== null) {
      setUseInstagrapi(savedMode === 'true');
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleSwitchMode = (useInstagrapi) => {
    setUseInstagrapi(useInstagrapi);
    // Lưu trạng thái vào localStorage
    localStorage.setItem(LOCAL_STORAGE_KEY, useInstagrapi.toString());
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageSquare className="h-6 w-6 text-purple-600" />
            <h1 className="text-xl font-semibold text-gray-900">Tin nhắn tự động</h1>
          </div>
        </div>

        <div className="mt-3 text-sm text-gray-600">
          <p>Quản lý cài đặt Instagram Messenger và mẫu tin nhắn tự động gửi khi in bình luận.</p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Messenger Switcher */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <MessengerSwitcher onSwitchMode={handleSwitchMode} />
        </div>

        {/* Messenger Control or Instagrapi Settings based on mode */}
        {useInstagrapi ? (
          <InstagrapiSettings />
        ) : (
          <MessengerControl />
        )}

        {/* Auto Message Control */}
        <AutoMessageControl />

        {/* Message Queue Manager */}
        <MessageQueueManager />

        {/* Message Templates */}
        <MessageTemplates />

        {/* Price Mappings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <PriceMappings />
        </div>

        {/* Failed Messages */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <FailedMessages />
        </div>
      </div>
    </div>
  );
};

export default AutoMessages;
