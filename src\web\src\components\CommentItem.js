import React, { useState, useEffect } from 'react';
import { Star, X, Printer, Package, User } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useCancelSettings } from '../contexts/CancelSettingsContext';
import { showPrintToast, showErrorToast } from '../utils/toastManager';
import moment from 'moment';

const CommentItem = ({
  comment,
  isRegular,
  currentColors,
  regularCustomers,
  toggleRegularCustomer,
  isConnected,
  queueStatus,
  unsendEnabled,
  unsendDisabled,
  queueMeta,
  onUnsendSuccess
}) => {
  const { printComment } = useSocket();
  const { settings, isCancelEnabled } = useCancelSettings();
  const [isLoading, setIsLoading] = useState(false);
  const [showCancel, setShowCancel] = useState(false);
  const [cancelTimeout, setCancelTimeout] = useState(null);
  const [progressInterval, setProgressInterval] = useState(null);
  const [progress, setProgress] = useState(100);
  const [activeAction, setActiveAction] = useState(null); // 'print' or 'backup'
  const [avatarError, setAvatarError] = useState(false);
  const [isUnsendLoading, setIsUnsendLoading] = useState(false);

  // Helper function to get proxy URL for Instagram images
  const getProxyImageUrl = (originalUrl) => {
    if (!originalUrl) return null;

    // Use a CORS proxy to bypass Instagram's CORS restrictions
    // Options:
    // 1. Local proxy - if your backend can proxy the request
    return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;

    // 2. Using the original URL directly (relies on CORS being properly configured)
    // return originalUrl;
  };

  const handleButtonClick = (action) => {
    if (!isConnected) {
      showErrorToast('Không có kết nối với server');
      return;
    }

    // Check if cancel is enabled for this action
    if (!isCancelEnabled(action)) {
      // If cancel is disabled, execute action immediately
      executeAction(action);
      return;
    }

    setActiveAction(action);
    setShowCancel(true);
    setProgress(100);

    // Clear any existing timeouts/intervals
    if (cancelTimeout) {
      clearTimeout(cancelTimeout);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    // Use duration from settings
    const duration = settings.duration;
    const interval = 16; // ~60fps
    const steps = duration / interval;
    const progressStep = 100 / steps;

    let currentProgress = 100;
    const newProgressInterval = setInterval(() => {
      currentProgress -= progressStep;
      if (currentProgress <= 0) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
        setProgress(0);
        setShowCancel(false);
        executeAction(action);
      } else {
        setProgress(currentProgress);
      }
    }, interval);

    setProgressInterval(newProgressInterval);

    // Set timeout as backup
    const timeout = setTimeout(() => {
      if (newProgressInterval) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
      }
      setShowCancel(false);
      executeAction(action);
    }, duration);

    setCancelTimeout(timeout);
  };

  const handleCancel = () => {
    setShowCancel(false);
    setProgress(100);
    setActiveAction(null);
    if (cancelTimeout) {
      clearTimeout(cancelTimeout);
      setCancelTimeout(null);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
  };

  const executeAction = async (action) => {
    setIsLoading(true);
    setActiveAction(null);

    try {
      const templateType = action === 'backup' ? 'backup_notification' : 'print_notification';
      const response = await printComment(comment, templateType);

      const isRegularCustomer = regularCustomers.has(comment.username);
      const customerTypeText = isRegularCustomer ? 'khách cũ' : 'khách mới';
      const actionText = action === 'backup' ? 'dự bị' : 'in';

      showPrintToast(`Đã ${actionText} bình luận của @${comment.username} (${customerTypeText})`);
      console.log(`${action} response:`, response);
    } catch (error) {
      const actionText = action === 'backup' ? 'dự bị' : 'in';
      showErrorToast(`Lỗi khi ${actionText} bình luận: ` + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Sử dụng màu nền đã được xử lý từ Comments.js
  // Không ghi đè màu nữa để tránh xung đột
  let customBg = currentColors.background;

  // Hàm thu hồi tin nhắn
  const handleUnsend = async () => {
    if (queueStatus === 'processing') return; // Không cho thu hồi khi đang xử lý
    setIsUnsendLoading(true);
    try {
      let res, data;
      if (queueStatus === 'pending') {
        // Chỉ xóa khỏi queue và printed_history
        res = await fetch(`/api/instagram/unsend/${queueMeta.id || queueMeta.messageId || queueMeta.instagram_message_id}`, { method: 'DELETE' });
        data = await res.json();
      } else if (queueStatus === 'completed') {
        // Gọi unsend như cũ
        res = await fetch(`/api/instagram/unsend/${queueMeta.instagram_message_id}`, { method: 'DELETE' });
        data = await res.json();
      }
      if (data && data.success) {
        const actionText = data.type === 'pending' ? 'xóa khỏi hàng chờ' : 'thu hồi tin nhắn';
        const historyText = data.deletedHistoryRecords > 0 ? ` và xóa ${data.deletedHistoryRecords} record khỏi lịch sử` : '';
        showPrintToast(`Đã ${actionText} thành công${historyText}!`);
        if (onUnsendSuccess) onUnsendSuccess();
      } else {
        const errorMsg = data?.error || 'Thu hồi tin nhắn thất bại';
        showErrorToast(errorMsg);
        console.error('Unsend error:', data);
      }
    } catch (e) {
      showErrorToast('Thu hồi tin nhắn thất bại');
    } finally {
      setIsUnsendLoading(false);
    }
  };

  // Cleanup timeouts and intervals on unmount
  useEffect(() => {
    return () => {
      if (cancelTimeout) {
        clearTimeout(cancelTimeout);
      }
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [cancelTimeout, progressInterval]);

  // Kiểm tra xem ảnh có tồn tại hay không
  const hasValidAvatar = !avatarError && comment.profile_pic_url && comment.profile_pic_url !== 'null' && comment.profile_pic_url !== 'undefined';

  return (
    <div
      className="flex items-center p-2 sm:p-3 rounded transition-colors gap-2 sm:gap-3"
      style={{
        backgroundColor: customBg,
        borderColor: currentColors.border,
        borderWidth: '1px',
        borderStyle: 'solid'
      }}
    >
      {/* 1. Avatar */}
      <div className="flex-shrink-0 flex items-center justify-center">
        <div
          onClick={() => toggleRegularCustomer(comment.username)}
          className={`rounded-full transition-colors touch-manipulation flex items-center justify-center relative cursor-pointer ${regularCustomers.has(comment.username)
            ? 'ring-2 ring-yellow-500'
            : 'hover:ring-1 hover:ring-gray-300'
            }`}
          title={regularCustomers.has(comment.username) ? 'Bỏ đánh dấu khách quen' : 'Đánh dấu khách quen'}
        >
          {hasValidAvatar ? (
            <img
              src={getProxyImageUrl(comment.profile_pic_url)}
              alt={`Avatar của ${comment.username}`}
              className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover"
              onError={() => setAvatarError(true)}
              loading="lazy"
              crossOrigin="anonymous"
            />
          ) : (
            <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center text-white
              ${regularCustomers.has(comment.username) ? 'bg-yellow-500' : 'bg-indigo-500'}`}>
              <span className="text-lg font-bold">
                {comment.username.charAt(0).toUpperCase()}
              </span>
            </div>
          )}

          {/* Star indicator for regular customers */}
          {regularCustomers.has(comment.username) && (
            <div className="absolute -top-1 -right-1 bg-yellow-500 text-white rounded-full p-0.5 shadow-sm">
              <Star className="h-3 w-3 fill-current" />
            </div>
          )}
        </div>
      </div>

      {/* 2. Username, Timestamp and Comment content */}
      <div className="flex-1 min-w-0 flex flex-col justify-center">
        {/* Timestamp and Username */}
        <div className="mb-0.5 sm:mb-1">
          <div className="text-sm sm:text-base text-gray-500 mb-1">
            {comment.timestamp ? (() => {
              try {
                return moment(comment.timestamp).format('HH:mm:ss');
              } catch (e) {
                console.error('Timestamp parsing error:', e, comment.timestamp);
                return new Date(comment.timestamp).toLocaleTimeString('vi-VN', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                });
              }
            })() : 'N/A'}
          </div>
          <div
            className="text-lg sm:text-xl lg:text-2xl font-medium truncate"
            style={{ color: currentColors.username }}
          >
            {comment.username && comment.username.length > 18
              ? comment.username.substring(0, 18) + '...'
              : comment.username}
          </div>
        </div>

        {/* Comment text */}
        <p
          className="text-lg sm:text-xl lg:text-2xl break-words leading-relaxed"
          style={{ color: currentColors.text }}
        >
          {comment.text}
        </p>
      </div>

      {/* 3. Action buttons (Backup and Print) or Cancel button */}
      <div className="flex-shrink-0 flex items-center">
        {showCancel ? (
          // Single Cancel button with progress animation
          <div className="relative">
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="flex items-center justify-center space-x-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded px-9 py-5 sm:px-10 sm:py-5.5 text-sm transition-colors touch-manipulation min-w-[89px] min-h-[62px]"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden xs:inline">Hủy</span>
            </button>

            {/* Progress border overlay around button */}
            <div
              className="absolute inset-0 pointer-events-none rounded"
              style={{
                background: `conic-gradient(from 0deg,
                  #ffffff 0deg,
                  #ffffff ${progress * 3.6}deg,
                  transparent ${progress * 3.6}deg,
                  transparent 360deg)`,
                mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                maskComposite: 'xor',
                WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                WebkitMaskComposite: 'xor',
                padding: '3px',
                borderRadius: 'inherit',
                filter: 'drop-shadow(0 0 2px rgba(255, 255, 255, 0.8))'
              }}
            />
          </div>
        ) : (
          <div className="flex items-center gap-4 sm:gap-5">
            {queueStatus ? (
              <button
                onClick={handleUnsend}
                disabled={queueStatus === 'processing' || unsendDisabled || isUnsendLoading}
                className={`flex items-center justify-center space-x-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded px-9 py-5 sm:px-10 sm:py-5.5 text-sm transition-colors touch-manipulation min-w-[89px] min-h-[62px] ${queueStatus === 'processing' || unsendDisabled || isUnsendLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                title="Thu hồi tin nhắn"
              >
                <span>{isUnsendLoading ? 'Đang thu hồi...' : 'Thu hồi'}</span>
              </button>
            ) : (
              <>
                <button
                  onClick={() => handleButtonClick('backup')}
                  disabled={!isConnected || isLoading}
                  className="flex items-center justify-center space-x-1 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded px-3 py-3 sm:px-4 sm:py-3.5 text-sm transition-colors touch-manipulation min-w-[50px] min-h-[48px]"
                >
                  <Package className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden xs:inline">{isLoading && activeAction === 'backup' ? 'Đang dự bị...' : 'Dự bị'}</span>
                </button>

                <button
                  onClick={() => handleButtonClick('print')}
                  disabled={!isConnected || isLoading}
                  className="flex items-center justify-center space-x-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded px-9 py-5 sm:px-10 sm:py-5.5 text-sm transition-colors touch-manipulation min-w-[89px] min-h-[62px]"
                >
                  <Printer className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden xs:inline">{isLoading && activeAction === 'print' ? 'Đang in...' : 'In'}</span>
                </button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentItem;
